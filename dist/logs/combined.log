{"context":"NestFactory","level":"info","message":"Starting Nest application...","timestamp":"2025-06-15T09:54:24.758Z"}
{"context":"TranslationService","level":"info","message":"加载翻译配置 - 模式: default, 模型: qwen-turbo","timestamp":"2025-06-15T09:54:24.778Z"}
{"context":"TranslationService","level":"info","message":"加载主题提取配置 - 模式: default, 模型: qwen-turbo","timestamp":"2025-06-15T09:54:24.778Z"}
{"context":"TranslationService","level":"info","message":"翻译服务初始化完成 - 翻译模型: qwen-turbo, 主题提取模型: qwen-turbo","timestamp":"2025-06-15T09:54:24.778Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-06-15T09:54:24.779Z"}
{"context":"InstanceLoader","level":"info","message":"PassportModule dependencies initialized","timestamp":"2025-06-15T09:54:24.779Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-06-15T09:54:24.779Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigHostModule dependencies initialized","timestamp":"2025-06-15T09:54:24.780Z"}
{"context":"InstanceLoader","level":"info","message":"DiscoveryModule dependencies initialized","timestamp":"2025-06-15T09:54:24.780Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-06-15T09:54:24.780Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-06-15T09:54:24.780Z"}
{"context":"InstanceLoader","level":"info","message":"ScheduleModule dependencies initialized","timestamp":"2025-06-15T09:54:24.781Z"}
{"context":"InstanceLoader","level":"info","message":"ScheduleModule dependencies initialized","timestamp":"2025-06-15T09:54:24.781Z"}
{"context":"InstanceLoader","level":"info","message":"ScheduleModule dependencies initialized","timestamp":"2025-06-15T09:54:24.781Z"}
{"context":"InstanceLoader","level":"info","message":"ElasticsearchModule dependencies initialized","timestamp":"2025-06-15T09:54:24.783Z"}
{"context":"InstanceLoader","level":"info","message":"JwtModule dependencies initialized","timestamp":"2025-06-15T09:54:24.783Z"}
{"context":"ElasticsearchOrbitService","level":"info","message":"TLE查询配置加载完成: 时间窗口=30分钟, 最大记录数=50000","timestamp":"2025-06-15T09:54:24.785Z"}
{"context":"InstanceLoader","level":"info","message":"OrbitAnalysisModule dependencies initialized","timestamp":"2025-06-15T09:54:24.786Z"}
{"context":"NewsSchedulerService","level":"info","message":"新闻定时任务服务初始化完成，配置: {\"enabled\":true,\"cronExpression\":\"0 5,12,18 * * *\",\"timezone\":\"Asia/Shanghai\"}","timestamp":"2025-06-15T09:54:24.787Z"}
{"context":"InstanceLoader","level":"info","message":"ESModule dependencies initialized","timestamp":"2025-06-15T09:54:24.788Z"}
{"context":"InstanceLoader","level":"info","message":"NewsSchedulerModule dependencies initialized","timestamp":"2025-06-15T09:54:24.788Z"}
{"context":"InstanceLoader","level":"info","message":"SatelliteTilesModule dependencies initialized","timestamp":"2025-06-15T09:54:24.788Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmCoreModule dependencies initialized","timestamp":"2025-06-15T09:54:24.860Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-06-15T09:54:24.860Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-06-15T09:54:24.860Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-06-15T09:54:24.860Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-06-15T09:54:24.860Z"}
{"context":"InstanceLoader","level":"info","message":"UserModule dependencies initialized","timestamp":"2025-06-15T09:54:24.861Z"}
{"context":"InstanceLoader","level":"info","message":"AppModule dependencies initialized","timestamp":"2025-06-15T09:54:24.861Z"}
{"context":"SatelliteSchedulerService","level":"info","message":"卫星数据定时任务服务初始化完成，配置: {\"enabled\":true,\"cronExpression\":\"0 3 * * *\",\"timezone\":\"Asia/Shanghai\",\"saveToDatabase\":true}","timestamp":"2025-06-15T09:54:24.861Z"}
{"context":"InstanceLoader","level":"info","message":"DatabaseModule dependencies initialized","timestamp":"2025-06-15T09:54:24.862Z"}
{"context":"InstanceLoader","level":"info","message":"AggregationTaskModule dependencies initialized","timestamp":"2025-06-15T09:54:24.862Z"}
{"context":"InstanceLoader","level":"info","message":"AuthModule dependencies initialized","timestamp":"2025-06-15T09:54:24.862Z"}
{"context":"InstanceLoader","level":"info","message":"SatelliteModule dependencies initialized","timestamp":"2025-06-15T09:54:24.862Z"}
{"context":"InstanceLoader","level":"info","message":"SatelliteSchedulerModule dependencies initialized","timestamp":"2025-06-15T09:54:24.862Z"}
{"context":"RoutesResolver","level":"info","message":"SatelliteAggregationController {/local/satellite-aggregation}:","timestamp":"2025-06-15T09:54:24.925Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite-aggregation/status, GET} route","timestamp":"2025-06-15T09:54:24.926Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite-aggregation/running, GET} route","timestamp":"2025-06-15T09:54:24.927Z"}
{"context":"RoutesResolver","level":"info","message":"AuthController {/auth}:","timestamp":"2025-06-15T09:54:24.927Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/register, POST} route","timestamp":"2025-06-15T09:54:24.927Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/login, POST} route","timestamp":"2025-06-15T09:54:24.927Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/users/:id/role, PUT} route","timestamp":"2025-06-15T09:54:24.927Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/users/:id/permissions, GET} route","timestamp":"2025-06-15T09:54:24.927Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/users, GET} route","timestamp":"2025-06-15T09:54:24.927Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/profile, GET} route","timestamp":"2025-06-15T09:54:24.928Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/admin/pending-users, GET} route","timestamp":"2025-06-15T09:54:24.928Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/admin/users/:id/approval, PUT} route","timestamp":"2025-06-15T09:54:24.928Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/admin/user-approval-history, GET} route","timestamp":"2025-06-15T09:54:24.928Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/admin/users/:id/delete, DELETE} route","timestamp":"2025-06-15T09:54:24.928Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/change-password, PUT} route","timestamp":"2025-06-15T09:54:24.928Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchDebrisEventController {/api/debris-events}:","timestamp":"2025-06-15T09:54:24.928Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/debris-events/search, POST} route","timestamp":"2025-06-15T09:54:24.928Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchConstellationController {/constellation}:","timestamp":"2025-06-15T09:54:24.929Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/constellation/search, POST} route","timestamp":"2025-06-15T09:54:24.929Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/constellation/names, GET} route","timestamp":"2025-06-15T09:54:24.929Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/constellation/organizations, GET} route","timestamp":"2025-06-15T09:54:24.929Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/constellation/purposes, GET} route","timestamp":"2025-06-15T09:54:24.929Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchCorrelationController {/correlation}:","timestamp":"2025-06-15T09:54:24.929Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/correlation/debris-to-event, POST} route","timestamp":"2025-06-15T09:54:24.929Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/correlation/event-to-debris, POST} route","timestamp":"2025-06-15T09:54:24.929Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchDebrisController {/debris}:","timestamp":"2025-06-15T09:54:24.929Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/search, POST} route","timestamp":"2025-06-15T09:54:24.929Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/names, GET} route","timestamp":"2025-06-15T09:54:24.929Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/object-classes, GET} route","timestamp":"2025-06-15T09:54:24.930Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/missions, GET} route","timestamp":"2025-06-15T09:54:24.930Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/countries, GET} route","timestamp":"2025-06-15T09:54:24.930Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/debug-query-dto, GET} route","timestamp":"2025-06-15T09:54:24.930Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchSatelliteController {/satellite}:","timestamp":"2025-06-15T09:54:24.930Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/satellite/search, POST} route","timestamp":"2025-06-15T09:54:24.930Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/satellite/names, GET} route","timestamp":"2025-06-15T09:54:24.930Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/satellite/statuses, GET} route","timestamp":"2025-06-15T09:54:24.930Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/satellite/test-direct-query, POST} route","timestamp":"2025-06-15T09:54:24.930Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchFreqController {/freq}:","timestamp":"2025-06-15T09:54:24.930Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/freq/search, POST} route","timestamp":"2025-06-15T09:54:24.930Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchOrbitController {/orbit}:","timestamp":"2025-06-15T09:54:24.931Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/search, POST} route","timestamp":"2025-06-15T09:54:24.931Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/bulk-tle, POST} route","timestamp":"2025-06-15T09:54:24.931Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/bulk-tle/all, POST} route","timestamp":"2025-06-15T09:54:24.931Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/tle-config, GET} route","timestamp":"2025-06-15T09:54:24.931Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/tle-config, PUT} route","timestamp":"2025-06-15T09:54:24.931Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchLoopholeController {/loophole}:","timestamp":"2025-06-15T09:54:24.931Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/loophole/search, POST} route","timestamp":"2025-06-15T09:54:24.931Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchNewsController {/api/es/news}:","timestamp":"2025-06-15T09:54:24.931Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/indices, GET} route","timestamp":"2025-06-15T09:54:24.932Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/translation-status, GET} route","timestamp":"2025-06-15T09:54:24.932Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/translate, POST} route","timestamp":"2025-06-15T09:54:24.932Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/api-stats, GET} route","timestamp":"2025-06-15T09:54:24.932Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/failure-stats, GET} route","timestamp":"2025-06-15T09:54:24.932Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/reset-failure-stats, POST} route","timestamp":"2025-06-15T09:54:24.932Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/failed-translations, GET} route","timestamp":"2025-06-15T09:54:24.932Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/retry-failed, POST} route","timestamp":"2025-06-15T09:54:24.932Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/extract-themes, POST} route","timestamp":"2025-06-15T09:54:24.932Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/list, POST} route","timestamp":"2025-06-15T09:54:24.932Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/hot-themes, GET} route","timestamp":"2025-06-15T09:54:24.932Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchLaunchController {/api/es/launch}:","timestamp":"2025-06-15T09:54:24.932Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/search, POST} route","timestamp":"2025-06-15T09:54:24.933Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/rocket-names, GET} route","timestamp":"2025-06-15T09:54:24.933Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/site-names, GET} route","timestamp":"2025-06-15T09:54:24.933Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/providers, GET} route","timestamp":"2025-06-15T09:54:24.933Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/cospar, POST} route","timestamp":"2025-06-15T09:54:24.933Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/wiki-site-names, GET} route","timestamp":"2025-06-15T09:54:24.933Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/all-launch-sites, GET} route","timestamp":"2025-06-15T09:54:24.933Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/wiki-site-info, GET} route","timestamp":"2025-06-15T09:54:24.933Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/service-providers/search, POST} route","timestamp":"2025-06-15T09:54:24.933Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchRocketController {/api/es/rocket}:","timestamp":"2025-06-15T09:54:24.933Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/rocket/search, POST} route","timestamp":"2025-06-15T09:54:24.933Z"}
{"context":"RoutesResolver","level":"info","message":"OrbitCalculatorController {/api/orbit-calculator}:","timestamp":"2025-06-15T09:54:24.933Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/orbit-calculator/calculate-positions, POST} route","timestamp":"2025-06-15T09:54:24.933Z"}
{"context":"RoutesResolver","level":"info","message":"PassAnalysisController {/api/pass-analysis}:","timestamp":"2025-06-15T09:54:24.933Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/pass-analysis/pass-analysis, POST} route","timestamp":"2025-06-15T09:54:24.933Z"}
{"context":"RoutesResolver","level":"info","message":"ConjunctionAnalysisController {/api/conjunction-analysis}:","timestamp":"2025-06-15T09:54:24.933Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/conjunction-analysis/conjunction-analysis, POST} route","timestamp":"2025-06-15T09:54:24.934Z"}
{"context":"RoutesResolver","level":"info","message":"SatelliteController {/local/satellite}:","timestamp":"2025-06-15T09:54:24.934Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/search, POST} route","timestamp":"2025-06-15T09:54:24.934Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/names, GET} route","timestamp":"2025-06-15T09:54:24.934Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/statuses, GET} route","timestamp":"2025-06-15T09:54:24.934Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/sync, POST} route","timestamp":"2025-06-15T09:54:24.934Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/sync-all, POST} route","timestamp":"2025-06-15T09:54:24.934Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/aggregate, POST} route","timestamp":"2025-06-15T09:54:24.934Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/clear, POST} route","timestamp":"2025-06-15T09:54:24.934Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/orbit-classes-local, GET} route","timestamp":"2025-06-15T09:54:24.934Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/orbit-types-local, GET} route","timestamp":"2025-06-15T09:54:24.934Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/:id, GET} route","timestamp":"2025-06-15T09:54:24.934Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite, GET} route","timestamp":"2025-06-15T09:54:24.935Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/test-aggregation, POST} route","timestamp":"2025-06-15T09:54:24.935Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/test-custom-aggregation, POST} route","timestamp":"2025-06-15T09:54:24.935Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/aggregate-all, POST} route","timestamp":"2025-06-15T09:54:24.935Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/incremental-aggregate, POST} route","timestamp":"2025-06-15T09:54:24.935Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/update-orbit-info, POST} route","timestamp":"2025-06-15T09:54:24.935Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/update-users, POST} route","timestamp":"2025-06-15T09:54:24.935Z"}
{"context":"RoutesResolver","level":"info","message":"ConstellationController {/local/constellation}:","timestamp":"2025-06-15T09:54:24.935Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/constellation, GET} route","timestamp":"2025-06-15T09:54:24.935Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/constellation/update, POST} route","timestamp":"2025-06-15T09:54:24.935Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/constellation/with-tle, GET} route","timestamp":"2025-06-15T09:54:24.935Z"}
{"context":"RoutesResolver","level":"info","message":"AggregationTaskController {/tasks/aggregation}:","timestamp":"2025-06-15T09:54:24.935Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation, GET} route","timestamp":"2025-06-15T09:54:24.936Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/latest, GET} route","timestamp":"2025-06-15T09:54:24.936Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/running, GET} route","timestamp":"2025-06-15T09:54:24.936Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id, GET} route","timestamp":"2025-06-15T09:54:24.936Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation, POST} route","timestamp":"2025-06-15T09:54:24.936Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id/start, POST} route","timestamp":"2025-06-15T09:54:24.936Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id/update-progress, POST} route","timestamp":"2025-06-15T09:54:24.936Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id/complete, POST} route","timestamp":"2025-06-15T09:54:24.936Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id/fail, POST} route","timestamp":"2025-06-15T09:54:24.936Z"}
{"context":"RoutesResolver","level":"info","message":"DatabaseController {/api/v1/database}:","timestamp":"2025-06-15T09:54:24.936Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/query, POST} route","timestamp":"2025-06-15T09:54:24.937Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/filter-satellites, POST} route","timestamp":"2025-06-15T09:54:24.937Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/satellite-users, GET} route","timestamp":"2025-06-15T09:54:24.937Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/satellite-purposes, GET} route","timestamp":"2025-06-15T09:54:24.937Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/launch-contractors, GET} route","timestamp":"2025-06-15T09:54:24.937Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/launch-sites, GET} route","timestamp":"2025-06-15T09:54:24.938Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/launch-vehicles, GET} route","timestamp":"2025-06-15T09:54:24.938Z"}
{"context":"RoutesResolver","level":"info","message":"SatelliteTilesController {/tiles}:","timestamp":"2025-06-15T09:54:24.938Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tiles/generate, POST} route","timestamp":"2025-06-15T09:54:24.938Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tiles/status, GET} route","timestamp":"2025-06-15T09:54:24.939Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tiles/satellites, GET} route","timestamp":"2025-06-15T09:54:24.939Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tiles/metadata, GET} route","timestamp":"2025-06-15T09:54:24.939Z"}
{"context":"RoutesResolver","level":"info","message":"NewsSchedulerController {/api/news-scheduler}:","timestamp":"2025-06-15T09:54:24.940Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/status, GET} route","timestamp":"2025-06-15T09:54:24.940Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/trigger, POST} route","timestamp":"2025-06-15T09:54:24.940Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/config, POST} route","timestamp":"2025-06-15T09:54:24.940Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/stop, POST} route","timestamp":"2025-06-15T09:54:24.940Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/cron-expressions, GET} route","timestamp":"2025-06-15T09:54:24.940Z"}
{"context":"RoutesResolver","level":"info","message":"SatelliteSchedulerController {/api/satellite-scheduler}:","timestamp":"2025-06-15T09:54:24.940Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/satellite-scheduler/status, GET} route","timestamp":"2025-06-15T09:54:24.941Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/satellite-scheduler/trigger, POST} route","timestamp":"2025-06-15T09:54:24.941Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/satellite-scheduler/config, POST} route","timestamp":"2025-06-15T09:54:24.941Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/satellite-scheduler/stop, POST} route","timestamp":"2025-06-15T09:54:24.941Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/satellite-scheduler/cron-expressions, GET} route","timestamp":"2025-06-15T09:54:24.941Z"}
{"context":"DatabaseService","level":"info","message":"正在检查并启用pg_trgm扩展...","timestamp":"2025-06-15T09:54:24.946Z"}
{"context":"DatabaseService","level":"info","message":"pg_trgm扩展已启用","timestamp":"2025-06-15T09:54:24.953Z"}
{"context":"NestApplication","level":"info","message":"Nest application successfully started","timestamp":"2025-06-15T09:54:24.966Z"}
{"context":"ElasticsearchRocketService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-06-15T09:54:24.971Z"}
{"context":"ElasticsearchSatelliteService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-06-15T09:54:24.972Z"}
{"context":"ElasticsearchConstellationService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-06-15T09:54:24.972Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-06-15T09:54:24.972Z"}
{"context":"ElasticsearchOrbitService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-06-15T09:54:24.972Z"}
{"context":"ElasticsearchLaunchService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-06-15T09:54:24.972Z"}
{"context":"ElasticsearchDebrisEventService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-06-15T09:54:24.972Z"}
{"context":"ElasticsearchBaseService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-06-15T09:54:24.972Z"}
{"context":"NestFactory","level":"info","message":"Starting Nest application...","timestamp":"2025-06-15T09:54:45.072Z"}
{"context":"TranslationService","level":"info","message":"加载翻译配置 - 模式: default, 模型: qwen-turbo","timestamp":"2025-06-15T09:54:45.093Z"}
{"context":"TranslationService","level":"info","message":"加载主题提取配置 - 模式: default, 模型: qwen-turbo","timestamp":"2025-06-15T09:54:45.093Z"}
{"context":"TranslationService","level":"info","message":"翻译服务初始化完成 - 翻译模型: qwen-turbo, 主题提取模型: qwen-turbo","timestamp":"2025-06-15T09:54:45.094Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-06-15T09:54:45.094Z"}
{"context":"InstanceLoader","level":"info","message":"PassportModule dependencies initialized","timestamp":"2025-06-15T09:54:45.094Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-06-15T09:54:45.095Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigHostModule dependencies initialized","timestamp":"2025-06-15T09:54:45.095Z"}
{"context":"InstanceLoader","level":"info","message":"DiscoveryModule dependencies initialized","timestamp":"2025-06-15T09:54:45.095Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-06-15T09:54:45.095Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-06-15T09:54:45.096Z"}
{"context":"InstanceLoader","level":"info","message":"ScheduleModule dependencies initialized","timestamp":"2025-06-15T09:54:45.096Z"}
{"context":"InstanceLoader","level":"info","message":"ScheduleModule dependencies initialized","timestamp":"2025-06-15T09:54:45.096Z"}
{"context":"InstanceLoader","level":"info","message":"ScheduleModule dependencies initialized","timestamp":"2025-06-15T09:54:45.096Z"}
{"context":"InstanceLoader","level":"info","message":"ElasticsearchModule dependencies initialized","timestamp":"2025-06-15T09:54:45.098Z"}
{"context":"InstanceLoader","level":"info","message":"JwtModule dependencies initialized","timestamp":"2025-06-15T09:54:45.098Z"}
{"context":"ElasticsearchOrbitService","level":"info","message":"TLE查询配置加载完成: 时间窗口=30分钟, 最大记录数=50000","timestamp":"2025-06-15T09:54:45.102Z"}
{"context":"InstanceLoader","level":"info","message":"OrbitAnalysisModule dependencies initialized","timestamp":"2025-06-15T09:54:45.103Z"}
{"context":"NewsSchedulerService","level":"info","message":"新闻定时任务服务初始化完成，配置: {\"enabled\":true,\"cronExpression\":\"0 5,12,18 * * *\",\"timezone\":\"Asia/Shanghai\"}","timestamp":"2025-06-15T09:54:45.104Z"}
{"context":"InstanceLoader","level":"info","message":"ESModule dependencies initialized","timestamp":"2025-06-15T09:54:45.105Z"}
{"context":"InstanceLoader","level":"info","message":"NewsSchedulerModule dependencies initialized","timestamp":"2025-06-15T09:54:45.105Z"}
{"context":"InstanceLoader","level":"info","message":"SatelliteTilesModule dependencies initialized","timestamp":"2025-06-15T09:54:45.105Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmCoreModule dependencies initialized","timestamp":"2025-06-15T09:54:45.133Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-06-15T09:54:45.133Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-06-15T09:54:45.133Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-06-15T09:54:45.133Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-06-15T09:54:45.133Z"}
{"context":"InstanceLoader","level":"info","message":"UserModule dependencies initialized","timestamp":"2025-06-15T09:54:45.135Z"}
{"context":"InstanceLoader","level":"info","message":"AppModule dependencies initialized","timestamp":"2025-06-15T09:54:45.135Z"}
{"context":"SatelliteSchedulerService","level":"info","message":"卫星数据定时任务服务初始化完成，配置: {\"enabled\":true,\"cronExpression\":\"0 3 * * *\",\"timezone\":\"Asia/Shanghai\",\"saveToDatabase\":true}","timestamp":"2025-06-15T09:54:45.136Z"}
{"context":"InstanceLoader","level":"info","message":"DatabaseModule dependencies initialized","timestamp":"2025-06-15T09:54:45.136Z"}
{"context":"InstanceLoader","level":"info","message":"AggregationTaskModule dependencies initialized","timestamp":"2025-06-15T09:54:45.136Z"}
{"context":"InstanceLoader","level":"info","message":"AuthModule dependencies initialized","timestamp":"2025-06-15T09:54:45.136Z"}
{"context":"InstanceLoader","level":"info","message":"SatelliteModule dependencies initialized","timestamp":"2025-06-15T09:54:45.136Z"}
{"context":"InstanceLoader","level":"info","message":"SatelliteSchedulerModule dependencies initialized","timestamp":"2025-06-15T09:54:45.136Z"}
{"context":"RoutesResolver","level":"info","message":"SatelliteAggregationController {/local/satellite-aggregation}:","timestamp":"2025-06-15T09:54:45.201Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite-aggregation/status, GET} route","timestamp":"2025-06-15T09:54:45.202Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite-aggregation/running, GET} route","timestamp":"2025-06-15T09:54:45.202Z"}
{"context":"RoutesResolver","level":"info","message":"AuthController {/auth}:","timestamp":"2025-06-15T09:54:45.202Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/register, POST} route","timestamp":"2025-06-15T09:54:45.203Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/login, POST} route","timestamp":"2025-06-15T09:54:45.203Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/users/:id/role, PUT} route","timestamp":"2025-06-15T09:54:45.203Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/users/:id/permissions, GET} route","timestamp":"2025-06-15T09:54:45.203Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/users, GET} route","timestamp":"2025-06-15T09:54:45.203Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/profile, GET} route","timestamp":"2025-06-15T09:54:45.203Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/admin/pending-users, GET} route","timestamp":"2025-06-15T09:54:45.203Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/admin/users/:id/approval, PUT} route","timestamp":"2025-06-15T09:54:45.204Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/admin/user-approval-history, GET} route","timestamp":"2025-06-15T09:54:45.204Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/admin/users/:id/delete, DELETE} route","timestamp":"2025-06-15T09:54:45.204Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/change-password, PUT} route","timestamp":"2025-06-15T09:54:45.204Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchDebrisEventController {/api/debris-events}:","timestamp":"2025-06-15T09:54:45.204Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/debris-events/search, POST} route","timestamp":"2025-06-15T09:54:45.204Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchConstellationController {/constellation}:","timestamp":"2025-06-15T09:54:45.204Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/constellation/search, POST} route","timestamp":"2025-06-15T09:54:45.204Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/constellation/names, GET} route","timestamp":"2025-06-15T09:54:45.204Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/constellation/organizations, GET} route","timestamp":"2025-06-15T09:54:45.204Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/constellation/purposes, GET} route","timestamp":"2025-06-15T09:54:45.204Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchCorrelationController {/correlation}:","timestamp":"2025-06-15T09:54:45.205Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/correlation/debris-to-event, POST} route","timestamp":"2025-06-15T09:54:45.205Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/correlation/event-to-debris, POST} route","timestamp":"2025-06-15T09:54:45.205Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchDebrisController {/debris}:","timestamp":"2025-06-15T09:54:45.205Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/search, POST} route","timestamp":"2025-06-15T09:54:45.205Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/names, GET} route","timestamp":"2025-06-15T09:54:45.205Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/object-classes, GET} route","timestamp":"2025-06-15T09:54:45.205Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/missions, GET} route","timestamp":"2025-06-15T09:54:45.205Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/countries, GET} route","timestamp":"2025-06-15T09:54:45.205Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/debug-query-dto, GET} route","timestamp":"2025-06-15T09:54:45.205Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchSatelliteController {/satellite}:","timestamp":"2025-06-15T09:54:45.205Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/satellite/search, POST} route","timestamp":"2025-06-15T09:54:45.206Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/satellite/names, GET} route","timestamp":"2025-06-15T09:54:45.206Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/satellite/statuses, GET} route","timestamp":"2025-06-15T09:54:45.206Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/satellite/test-direct-query, POST} route","timestamp":"2025-06-15T09:54:45.206Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchFreqController {/freq}:","timestamp":"2025-06-15T09:54:45.206Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/freq/search, POST} route","timestamp":"2025-06-15T09:54:45.206Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchOrbitController {/orbit}:","timestamp":"2025-06-15T09:54:45.206Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/search, POST} route","timestamp":"2025-06-15T09:54:45.206Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/bulk-tle, POST} route","timestamp":"2025-06-15T09:54:45.206Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/bulk-tle/all, POST} route","timestamp":"2025-06-15T09:54:45.206Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/tle-config, GET} route","timestamp":"2025-06-15T09:54:45.206Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/tle-config, PUT} route","timestamp":"2025-06-15T09:54:45.207Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchLoopholeController {/loophole}:","timestamp":"2025-06-15T09:54:45.207Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/loophole/search, POST} route","timestamp":"2025-06-15T09:54:45.207Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchNewsController {/api/es/news}:","timestamp":"2025-06-15T09:54:45.207Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/indices, GET} route","timestamp":"2025-06-15T09:54:45.207Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/translation-status, GET} route","timestamp":"2025-06-15T09:54:45.207Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/translate, POST} route","timestamp":"2025-06-15T09:54:45.207Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/api-stats, GET} route","timestamp":"2025-06-15T09:54:45.207Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/failure-stats, GET} route","timestamp":"2025-06-15T09:54:45.207Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/reset-failure-stats, POST} route","timestamp":"2025-06-15T09:54:45.207Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/failed-translations, GET} route","timestamp":"2025-06-15T09:54:45.207Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/retry-failed, POST} route","timestamp":"2025-06-15T09:54:45.208Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/extract-themes, POST} route","timestamp":"2025-06-15T09:54:45.208Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/list, POST} route","timestamp":"2025-06-15T09:54:45.208Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/hot-themes, GET} route","timestamp":"2025-06-15T09:54:45.208Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchLaunchController {/api/es/launch}:","timestamp":"2025-06-15T09:54:45.208Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/search, POST} route","timestamp":"2025-06-15T09:54:45.208Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/rocket-names, GET} route","timestamp":"2025-06-15T09:54:45.208Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/site-names, GET} route","timestamp":"2025-06-15T09:54:45.208Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/providers, GET} route","timestamp":"2025-06-15T09:54:45.208Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/cospar, POST} route","timestamp":"2025-06-15T09:54:45.208Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/wiki-site-names, GET} route","timestamp":"2025-06-15T09:54:45.208Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/all-launch-sites, GET} route","timestamp":"2025-06-15T09:54:45.208Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/wiki-site-info, GET} route","timestamp":"2025-06-15T09:54:45.208Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/service-providers/search, POST} route","timestamp":"2025-06-15T09:54:45.209Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchRocketController {/api/es/rocket}:","timestamp":"2025-06-15T09:54:45.209Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/rocket/search, POST} route","timestamp":"2025-06-15T09:54:45.209Z"}
{"context":"RoutesResolver","level":"info","message":"OrbitCalculatorController {/api/orbit-calculator}:","timestamp":"2025-06-15T09:54:45.209Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/orbit-calculator/calculate-positions, POST} route","timestamp":"2025-06-15T09:54:45.209Z"}
{"context":"RoutesResolver","level":"info","message":"PassAnalysisController {/api/pass-analysis}:","timestamp":"2025-06-15T09:54:45.209Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/pass-analysis/pass-analysis, POST} route","timestamp":"2025-06-15T09:54:45.209Z"}
{"context":"RoutesResolver","level":"info","message":"ConjunctionAnalysisController {/api/conjunction-analysis}:","timestamp":"2025-06-15T09:54:45.209Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/conjunction-analysis/conjunction-analysis, POST} route","timestamp":"2025-06-15T09:54:45.209Z"}
{"context":"RoutesResolver","level":"info","message":"SatelliteController {/local/satellite}:","timestamp":"2025-06-15T09:54:45.209Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/search, POST} route","timestamp":"2025-06-15T09:54:45.209Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/names, GET} route","timestamp":"2025-06-15T09:54:45.209Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/statuses, GET} route","timestamp":"2025-06-15T09:54:45.209Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/sync, POST} route","timestamp":"2025-06-15T09:54:45.209Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/sync-all, POST} route","timestamp":"2025-06-15T09:54:45.209Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/aggregate, POST} route","timestamp":"2025-06-15T09:54:45.209Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/clear, POST} route","timestamp":"2025-06-15T09:54:45.210Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/orbit-classes-local, GET} route","timestamp":"2025-06-15T09:54:45.210Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/orbit-types-local, GET} route","timestamp":"2025-06-15T09:54:45.210Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/:id, GET} route","timestamp":"2025-06-15T09:54:45.210Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite, GET} route","timestamp":"2025-06-15T09:54:45.210Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/test-aggregation, POST} route","timestamp":"2025-06-15T09:54:45.210Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/test-custom-aggregation, POST} route","timestamp":"2025-06-15T09:54:45.210Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/aggregate-all, POST} route","timestamp":"2025-06-15T09:54:45.210Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/incremental-aggregate, POST} route","timestamp":"2025-06-15T09:54:45.210Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/update-orbit-info, POST} route","timestamp":"2025-06-15T09:54:45.210Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/update-users, POST} route","timestamp":"2025-06-15T09:54:45.210Z"}
{"context":"RoutesResolver","level":"info","message":"ConstellationController {/local/constellation}:","timestamp":"2025-06-15T09:54:45.210Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/constellation, GET} route","timestamp":"2025-06-15T09:54:45.210Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/constellation/update, POST} route","timestamp":"2025-06-15T09:54:45.211Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/constellation/with-tle, GET} route","timestamp":"2025-06-15T09:54:45.211Z"}
{"context":"RoutesResolver","level":"info","message":"AggregationTaskController {/tasks/aggregation}:","timestamp":"2025-06-15T09:54:45.211Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation, GET} route","timestamp":"2025-06-15T09:54:45.211Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/latest, GET} route","timestamp":"2025-06-15T09:54:45.211Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/running, GET} route","timestamp":"2025-06-15T09:54:45.211Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id, GET} route","timestamp":"2025-06-15T09:54:45.213Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation, POST} route","timestamp":"2025-06-15T09:54:45.213Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id/start, POST} route","timestamp":"2025-06-15T09:54:45.213Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id/update-progress, POST} route","timestamp":"2025-06-15T09:54:45.213Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id/complete, POST} route","timestamp":"2025-06-15T09:54:45.213Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id/fail, POST} route","timestamp":"2025-06-15T09:54:45.213Z"}
{"context":"RoutesResolver","level":"info","message":"DatabaseController {/api/v1/database}:","timestamp":"2025-06-15T09:54:45.213Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/query, POST} route","timestamp":"2025-06-15T09:54:45.213Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/filter-satellites, POST} route","timestamp":"2025-06-15T09:54:45.213Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/satellite-users, GET} route","timestamp":"2025-06-15T09:54:45.213Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/satellite-purposes, GET} route","timestamp":"2025-06-15T09:54:45.214Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/launch-contractors, GET} route","timestamp":"2025-06-15T09:54:45.214Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/launch-sites, GET} route","timestamp":"2025-06-15T09:54:45.214Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/launch-vehicles, GET} route","timestamp":"2025-06-15T09:54:45.214Z"}
{"context":"RoutesResolver","level":"info","message":"SatelliteTilesController {/tiles}:","timestamp":"2025-06-15T09:54:45.214Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tiles/generate, POST} route","timestamp":"2025-06-15T09:54:45.214Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tiles/status, GET} route","timestamp":"2025-06-15T09:54:45.214Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tiles/satellites, GET} route","timestamp":"2025-06-15T09:54:45.214Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tiles/metadata, GET} route","timestamp":"2025-06-15T09:54:45.214Z"}
{"context":"RoutesResolver","level":"info","message":"NewsSchedulerController {/api/news-scheduler}:","timestamp":"2025-06-15T09:54:45.214Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/status, GET} route","timestamp":"2025-06-15T09:54:45.214Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/trigger, POST} route","timestamp":"2025-06-15T09:54:45.214Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/config, POST} route","timestamp":"2025-06-15T09:54:45.214Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/stop, POST} route","timestamp":"2025-06-15T09:54:45.214Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/cron-expressions, GET} route","timestamp":"2025-06-15T09:54:45.214Z"}
{"context":"RoutesResolver","level":"info","message":"SatelliteSchedulerController {/api/satellite-scheduler}:","timestamp":"2025-06-15T09:54:45.215Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/satellite-scheduler/status, GET} route","timestamp":"2025-06-15T09:54:45.215Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/satellite-scheduler/trigger, POST} route","timestamp":"2025-06-15T09:54:45.215Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/satellite-scheduler/config, POST} route","timestamp":"2025-06-15T09:54:45.215Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/satellite-scheduler/stop, POST} route","timestamp":"2025-06-15T09:54:45.215Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/satellite-scheduler/cron-expressions, GET} route","timestamp":"2025-06-15T09:54:45.215Z"}
{"context":"DatabaseService","level":"info","message":"正在检查并启用pg_trgm扩展...","timestamp":"2025-06-15T09:54:45.219Z"}
{"context":"DatabaseService","level":"info","message":"pg_trgm扩展已启用","timestamp":"2025-06-15T09:54:45.222Z"}
{"context":"NestApplication","level":"info","message":"Nest application successfully started","timestamp":"2025-06-15T09:54:45.234Z"}
{"context":"ElasticsearchSatelliteService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-06-15T09:54:45.236Z"}
{"context":"ElasticsearchLaunchService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-06-15T09:54:45.237Z"}
{"context":"ElasticsearchDebrisEventService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-06-15T09:54:45.237Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-06-15T09:54:45.238Z"}
{"context":"ElasticsearchConstellationService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-06-15T09:54:45.238Z"}
{"context":"ElasticsearchRocketService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-06-15T09:54:45.238Z"}
{"context":"ElasticsearchOrbitService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-06-15T09:54:45.238Z"}
{"context":"ElasticsearchBaseService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-06-15T09:54:45.238Z"}
{"context":"NestFactory","level":"info","message":"Starting Nest application...","timestamp":"2025-06-15T09:55:22.544Z"}
{"context":"TranslationService","level":"info","message":"加载翻译配置 - 模式: default, 模型: qwen-turbo","timestamp":"2025-06-15T09:55:22.562Z"}
{"context":"TranslationService","level":"info","message":"加载主题提取配置 - 模式: default, 模型: qwen-turbo","timestamp":"2025-06-15T09:55:22.562Z"}
{"context":"TranslationService","level":"info","message":"翻译服务初始化完成 - 翻译模型: qwen-turbo, 主题提取模型: qwen-turbo","timestamp":"2025-06-15T09:55:22.563Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-06-15T09:55:22.564Z"}
{"context":"InstanceLoader","level":"info","message":"PassportModule dependencies initialized","timestamp":"2025-06-15T09:55:22.564Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-06-15T09:55:22.564Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigHostModule dependencies initialized","timestamp":"2025-06-15T09:55:22.564Z"}
{"context":"InstanceLoader","level":"info","message":"DiscoveryModule dependencies initialized","timestamp":"2025-06-15T09:55:22.564Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-06-15T09:55:22.565Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-06-15T09:55:22.565Z"}
{"context":"InstanceLoader","level":"info","message":"ScheduleModule dependencies initialized","timestamp":"2025-06-15T09:55:22.565Z"}
{"context":"InstanceLoader","level":"info","message":"ScheduleModule dependencies initialized","timestamp":"2025-06-15T09:55:22.565Z"}
{"context":"InstanceLoader","level":"info","message":"ScheduleModule dependencies initialized","timestamp":"2025-06-15T09:55:22.565Z"}
{"context":"InstanceLoader","level":"info","message":"ElasticsearchModule dependencies initialized","timestamp":"2025-06-15T09:55:22.568Z"}
{"context":"InstanceLoader","level":"info","message":"JwtModule dependencies initialized","timestamp":"2025-06-15T09:55:22.568Z"}
{"context":"ElasticsearchOrbitService","level":"info","message":"TLE查询配置加载完成: 时间窗口=30分钟, 最大记录数=50000","timestamp":"2025-06-15T09:55:22.571Z"}
{"context":"InstanceLoader","level":"info","message":"OrbitAnalysisModule dependencies initialized","timestamp":"2025-06-15T09:55:22.572Z"}
{"context":"NewsSchedulerService","level":"info","message":"新闻定时任务服务初始化完成，配置: {\"enabled\":true,\"cronExpression\":\"0 5,12,18 * * *\",\"timezone\":\"Asia/Shanghai\"}","timestamp":"2025-06-15T09:55:22.572Z"}
{"context":"InstanceLoader","level":"info","message":"ESModule dependencies initialized","timestamp":"2025-06-15T09:55:22.573Z"}
{"context":"InstanceLoader","level":"info","message":"NewsSchedulerModule dependencies initialized","timestamp":"2025-06-15T09:55:22.573Z"}
{"context":"InstanceLoader","level":"info","message":"SatelliteTilesModule dependencies initialized","timestamp":"2025-06-15T09:55:22.573Z"}
{"context":"ElasticsearchSatelliteService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-06-15T09:55:22.607Z"}
{"context":"ElasticsearchRocketService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-06-15T09:55:22.607Z"}
{"context":"ElasticsearchDebrisEventService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-06-15T09:55:22.607Z"}
{"context":"ElasticsearchLaunchService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-06-15T09:55:22.608Z"}
{"context":"ElasticsearchConstellationService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-06-15T09:55:22.608Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-06-15T09:55:22.608Z"}
{"context":"ElasticsearchBaseService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-06-15T09:55:22.623Z"}
{"context":"ElasticsearchOrbitService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-06-15T09:55:22.623Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmCoreModule dependencies initialized","timestamp":"2025-06-15T09:55:22.654Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-06-15T09:55:22.654Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-06-15T09:55:22.654Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-06-15T09:55:22.654Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-06-15T09:55:22.654Z"}
{"context":"InstanceLoader","level":"info","message":"UserModule dependencies initialized","timestamp":"2025-06-15T09:55:22.654Z"}
{"context":"InstanceLoader","level":"info","message":"AppModule dependencies initialized","timestamp":"2025-06-15T09:55:22.654Z"}
{"context":"SatelliteSchedulerService","level":"info","message":"卫星数据定时任务服务初始化完成，配置: {\"enabled\":true,\"cronExpression\":\"0 3 * * *\",\"timezone\":\"Asia/Shanghai\",\"saveToDatabase\":true}","timestamp":"2025-06-15T09:55:22.655Z"}
{"context":"InstanceLoader","level":"info","message":"DatabaseModule dependencies initialized","timestamp":"2025-06-15T09:55:22.655Z"}
{"context":"InstanceLoader","level":"info","message":"AggregationTaskModule dependencies initialized","timestamp":"2025-06-15T09:55:22.655Z"}
{"context":"InstanceLoader","level":"info","message":"AuthModule dependencies initialized","timestamp":"2025-06-15T09:55:22.655Z"}
{"context":"InstanceLoader","level":"info","message":"SatelliteModule dependencies initialized","timestamp":"2025-06-15T09:55:22.655Z"}
{"context":"InstanceLoader","level":"info","message":"SatelliteSchedulerModule dependencies initialized","timestamp":"2025-06-15T09:55:22.655Z"}
{"context":"RoutesResolver","level":"info","message":"SatelliteAggregationController {/local/satellite-aggregation}:","timestamp":"2025-06-15T09:55:22.730Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite-aggregation/status, GET} route","timestamp":"2025-06-15T09:55:22.732Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite-aggregation/running, GET} route","timestamp":"2025-06-15T09:55:22.732Z"}
{"context":"RoutesResolver","level":"info","message":"AuthController {/auth}:","timestamp":"2025-06-15T09:55:22.732Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/register, POST} route","timestamp":"2025-06-15T09:55:22.732Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/login, POST} route","timestamp":"2025-06-15T09:55:22.732Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/users/:id/role, PUT} route","timestamp":"2025-06-15T09:55:22.733Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/users/:id/permissions, GET} route","timestamp":"2025-06-15T09:55:22.734Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/users, GET} route","timestamp":"2025-06-15T09:55:22.734Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/profile, GET} route","timestamp":"2025-06-15T09:55:22.734Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/admin/pending-users, GET} route","timestamp":"2025-06-15T09:55:22.734Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/admin/users/:id/approval, PUT} route","timestamp":"2025-06-15T09:55:22.735Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/admin/user-approval-history, GET} route","timestamp":"2025-06-15T09:55:22.735Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/admin/users/:id/delete, DELETE} route","timestamp":"2025-06-15T09:55:22.735Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/change-password, PUT} route","timestamp":"2025-06-15T09:55:22.736Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchDebrisEventController {/api/debris-events}:","timestamp":"2025-06-15T09:55:22.736Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/debris-events/search, POST} route","timestamp":"2025-06-15T09:55:22.737Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchConstellationController {/constellation}:","timestamp":"2025-06-15T09:55:22.737Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/constellation/search, POST} route","timestamp":"2025-06-15T09:55:22.737Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/constellation/names, GET} route","timestamp":"2025-06-15T09:55:22.737Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/constellation/organizations, GET} route","timestamp":"2025-06-15T09:55:22.737Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/constellation/purposes, GET} route","timestamp":"2025-06-15T09:55:22.737Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchCorrelationController {/correlation}:","timestamp":"2025-06-15T09:55:22.737Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/correlation/debris-to-event, POST} route","timestamp":"2025-06-15T09:55:22.737Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/correlation/event-to-debris, POST} route","timestamp":"2025-06-15T09:55:22.738Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchDebrisController {/debris}:","timestamp":"2025-06-15T09:55:22.738Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/search, POST} route","timestamp":"2025-06-15T09:55:22.738Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/names, GET} route","timestamp":"2025-06-15T09:55:22.738Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/object-classes, GET} route","timestamp":"2025-06-15T09:55:22.738Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/missions, GET} route","timestamp":"2025-06-15T09:55:22.738Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/countries, GET} route","timestamp":"2025-06-15T09:55:22.738Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/debug-query-dto, GET} route","timestamp":"2025-06-15T09:55:22.738Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchSatelliteController {/satellite}:","timestamp":"2025-06-15T09:55:22.738Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/satellite/search, POST} route","timestamp":"2025-06-15T09:55:22.738Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/satellite/names, GET} route","timestamp":"2025-06-15T09:55:22.738Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/satellite/statuses, GET} route","timestamp":"2025-06-15T09:55:22.739Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/satellite/test-direct-query, POST} route","timestamp":"2025-06-15T09:55:22.739Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchFreqController {/freq}:","timestamp":"2025-06-15T09:55:22.739Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/freq/search, POST} route","timestamp":"2025-06-15T09:55:22.739Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchOrbitController {/orbit}:","timestamp":"2025-06-15T09:55:22.739Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/search, POST} route","timestamp":"2025-06-15T09:55:22.739Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/bulk-tle, POST} route","timestamp":"2025-06-15T09:55:22.740Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/bulk-tle/all, POST} route","timestamp":"2025-06-15T09:55:22.740Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/tle-config, GET} route","timestamp":"2025-06-15T09:55:22.740Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/tle-config, PUT} route","timestamp":"2025-06-15T09:55:22.740Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchLoopholeController {/loophole}:","timestamp":"2025-06-15T09:55:22.740Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/loophole/search, POST} route","timestamp":"2025-06-15T09:55:22.740Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchNewsController {/api/es/news}:","timestamp":"2025-06-15T09:55:22.740Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/indices, GET} route","timestamp":"2025-06-15T09:55:22.741Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/translation-status, GET} route","timestamp":"2025-06-15T09:55:22.741Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/translate, POST} route","timestamp":"2025-06-15T09:55:22.741Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/api-stats, GET} route","timestamp":"2025-06-15T09:55:22.741Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/failure-stats, GET} route","timestamp":"2025-06-15T09:55:22.742Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/reset-failure-stats, POST} route","timestamp":"2025-06-15T09:55:22.742Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/failed-translations, GET} route","timestamp":"2025-06-15T09:55:22.742Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/retry-failed, POST} route","timestamp":"2025-06-15T09:55:22.742Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/extract-themes, POST} route","timestamp":"2025-06-15T09:55:22.742Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/list, POST} route","timestamp":"2025-06-15T09:55:22.742Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/hot-themes, GET} route","timestamp":"2025-06-15T09:55:22.742Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchLaunchController {/api/es/launch}:","timestamp":"2025-06-15T09:55:22.742Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/search, POST} route","timestamp":"2025-06-15T09:55:22.742Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/rocket-names, GET} route","timestamp":"2025-06-15T09:55:22.742Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/site-names, GET} route","timestamp":"2025-06-15T09:55:22.742Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/providers, GET} route","timestamp":"2025-06-15T09:55:22.742Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/cospar, POST} route","timestamp":"2025-06-15T09:55:22.743Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/wiki-site-names, GET} route","timestamp":"2025-06-15T09:55:22.743Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/all-launch-sites, GET} route","timestamp":"2025-06-15T09:55:22.743Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/wiki-site-info, GET} route","timestamp":"2025-06-15T09:55:22.743Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/service-providers/search, POST} route","timestamp":"2025-06-15T09:55:22.743Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchRocketController {/api/es/rocket}:","timestamp":"2025-06-15T09:55:22.743Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/rocket/search, POST} route","timestamp":"2025-06-15T09:55:22.743Z"}
{"context":"RoutesResolver","level":"info","message":"OrbitCalculatorController {/api/orbit-calculator}:","timestamp":"2025-06-15T09:55:22.743Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/orbit-calculator/calculate-positions, POST} route","timestamp":"2025-06-15T09:55:22.744Z"}
{"context":"RoutesResolver","level":"info","message":"PassAnalysisController {/api/pass-analysis}:","timestamp":"2025-06-15T09:55:22.744Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/pass-analysis/pass-analysis, POST} route","timestamp":"2025-06-15T09:55:22.744Z"}
{"context":"RoutesResolver","level":"info","message":"ConjunctionAnalysisController {/api/conjunction-analysis}:","timestamp":"2025-06-15T09:55:22.744Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/conjunction-analysis/conjunction-analysis, POST} route","timestamp":"2025-06-15T09:55:22.745Z"}
{"context":"RoutesResolver","level":"info","message":"SatelliteController {/local/satellite}:","timestamp":"2025-06-15T09:55:22.745Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/search, POST} route","timestamp":"2025-06-15T09:55:22.746Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/names, GET} route","timestamp":"2025-06-15T09:55:22.746Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/statuses, GET} route","timestamp":"2025-06-15T09:55:22.746Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/sync, POST} route","timestamp":"2025-06-15T09:55:22.747Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/sync-all, POST} route","timestamp":"2025-06-15T09:55:22.747Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/aggregate, POST} route","timestamp":"2025-06-15T09:55:22.747Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/clear, POST} route","timestamp":"2025-06-15T09:55:22.748Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/orbit-classes-local, GET} route","timestamp":"2025-06-15T09:55:22.748Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/orbit-types-local, GET} route","timestamp":"2025-06-15T09:55:22.748Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/:id, GET} route","timestamp":"2025-06-15T09:55:22.748Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite, GET} route","timestamp":"2025-06-15T09:55:22.749Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/test-aggregation, POST} route","timestamp":"2025-06-15T09:55:22.749Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/test-custom-aggregation, POST} route","timestamp":"2025-06-15T09:55:22.749Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/aggregate-all, POST} route","timestamp":"2025-06-15T09:55:22.749Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/incremental-aggregate, POST} route","timestamp":"2025-06-15T09:55:22.749Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/update-orbit-info, POST} route","timestamp":"2025-06-15T09:55:22.750Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/update-users, POST} route","timestamp":"2025-06-15T09:55:22.750Z"}
{"context":"RoutesResolver","level":"info","message":"ConstellationController {/local/constellation}:","timestamp":"2025-06-15T09:55:22.750Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/constellation, GET} route","timestamp":"2025-06-15T09:55:22.750Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/constellation/update, POST} route","timestamp":"2025-06-15T09:55:22.750Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/constellation/with-tle, GET} route","timestamp":"2025-06-15T09:55:22.751Z"}
{"context":"RoutesResolver","level":"info","message":"AggregationTaskController {/tasks/aggregation}:","timestamp":"2025-06-15T09:55:22.751Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation, GET} route","timestamp":"2025-06-15T09:55:22.751Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/latest, GET} route","timestamp":"2025-06-15T09:55:22.751Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/running, GET} route","timestamp":"2025-06-15T09:55:22.751Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id, GET} route","timestamp":"2025-06-15T09:55:22.751Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation, POST} route","timestamp":"2025-06-15T09:55:22.751Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id/start, POST} route","timestamp":"2025-06-15T09:55:22.751Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id/update-progress, POST} route","timestamp":"2025-06-15T09:55:22.752Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id/complete, POST} route","timestamp":"2025-06-15T09:55:22.752Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id/fail, POST} route","timestamp":"2025-06-15T09:55:22.752Z"}
{"context":"RoutesResolver","level":"info","message":"DatabaseController {/api/v1/database}:","timestamp":"2025-06-15T09:55:22.752Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/query, POST} route","timestamp":"2025-06-15T09:55:22.752Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/filter-satellites, POST} route","timestamp":"2025-06-15T09:55:22.752Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/satellite-users, GET} route","timestamp":"2025-06-15T09:55:22.753Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/satellite-purposes, GET} route","timestamp":"2025-06-15T09:55:22.753Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/launch-contractors, GET} route","timestamp":"2025-06-15T09:55:22.753Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/launch-sites, GET} route","timestamp":"2025-06-15T09:55:22.753Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/launch-vehicles, GET} route","timestamp":"2025-06-15T09:55:22.753Z"}
{"context":"RoutesResolver","level":"info","message":"SatelliteTilesController {/tiles}:","timestamp":"2025-06-15T09:55:22.753Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tiles/generate, POST} route","timestamp":"2025-06-15T09:55:22.754Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tiles/status, GET} route","timestamp":"2025-06-15T09:55:22.754Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tiles/satellites, GET} route","timestamp":"2025-06-15T09:55:22.754Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tiles/metadata, GET} route","timestamp":"2025-06-15T09:55:22.754Z"}
{"context":"RoutesResolver","level":"info","message":"NewsSchedulerController {/api/news-scheduler}:","timestamp":"2025-06-15T09:55:22.754Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/status, GET} route","timestamp":"2025-06-15T09:55:22.754Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/trigger, POST} route","timestamp":"2025-06-15T09:55:22.754Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/config, POST} route","timestamp":"2025-06-15T09:55:22.754Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/stop, POST} route","timestamp":"2025-06-15T09:55:22.754Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/cron-expressions, GET} route","timestamp":"2025-06-15T09:55:22.754Z"}
{"context":"RoutesResolver","level":"info","message":"SatelliteSchedulerController {/api/satellite-scheduler}:","timestamp":"2025-06-15T09:55:22.754Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/satellite-scheduler/status, GET} route","timestamp":"2025-06-15T09:55:22.754Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/satellite-scheduler/trigger, POST} route","timestamp":"2025-06-15T09:55:22.754Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/satellite-scheduler/config, POST} route","timestamp":"2025-06-15T09:55:22.754Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/satellite-scheduler/stop, POST} route","timestamp":"2025-06-15T09:55:22.755Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/satellite-scheduler/cron-expressions, GET} route","timestamp":"2025-06-15T09:55:22.755Z"}
{"context":"DatabaseService","level":"info","message":"正在检查并启用pg_trgm扩展...","timestamp":"2025-06-15T09:55:22.759Z"}
{"context":"DatabaseService","level":"info","message":"pg_trgm扩展已启用","timestamp":"2025-06-15T09:55:22.761Z"}
{"context":"NestApplication","level":"info","message":"Nest application successfully started","timestamp":"2025-06-15T09:55:22.772Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"找到12个新闻索引","timestamp":"2025-06-15T09:55:42.071Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"找到12个新闻索引","timestamp":"2025-06-15T09:55:42.080Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"找到12个新闻索引","timestamp":"2025-06-15T09:55:42.407Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"找到12个新闻索引","timestamp":"2025-06-15T09:55:42.408Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"找到12个新闻索引","timestamp":"2025-06-15T09:55:42.633Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"找到12个新闻索引","timestamp":"2025-06-15T09:55:42.658Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"找到12个新闻索引","timestamp":"2025-06-15T09:55:43.585Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"找到12个新闻索引","timestamp":"2025-06-15T09:55:43.587Z"}
{"context":"NestFactory","level":"info","message":"Starting Nest application...","timestamp":"2025-06-15T09:57:54.458Z"}
{"context":"TranslationService","level":"info","message":"加载翻译配置 - 模式: default, 模型: qwen-turbo","timestamp":"2025-06-15T09:57:54.477Z"}
{"context":"TranslationService","level":"info","message":"加载主题提取配置 - 模式: default, 模型: qwen-turbo","timestamp":"2025-06-15T09:57:54.478Z"}
{"context":"TranslationService","level":"info","message":"翻译服务初始化完成 - 翻译模型: qwen-turbo, 主题提取模型: qwen-turbo","timestamp":"2025-06-15T09:57:54.478Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-06-15T09:57:54.480Z"}
{"context":"InstanceLoader","level":"info","message":"PassportModule dependencies initialized","timestamp":"2025-06-15T09:57:54.480Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-06-15T09:57:54.481Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigHostModule dependencies initialized","timestamp":"2025-06-15T09:57:54.481Z"}
{"context":"InstanceLoader","level":"info","message":"DiscoveryModule dependencies initialized","timestamp":"2025-06-15T09:57:54.481Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-06-15T09:57:54.482Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-06-15T09:57:54.482Z"}
{"context":"InstanceLoader","level":"info","message":"ScheduleModule dependencies initialized","timestamp":"2025-06-15T09:57:54.482Z"}
{"context":"InstanceLoader","level":"info","message":"ScheduleModule dependencies initialized","timestamp":"2025-06-15T09:57:54.482Z"}
{"context":"InstanceLoader","level":"info","message":"ScheduleModule dependencies initialized","timestamp":"2025-06-15T09:57:54.482Z"}
{"context":"InstanceLoader","level":"info","message":"ElasticsearchModule dependencies initialized","timestamp":"2025-06-15T09:57:54.487Z"}
{"context":"InstanceLoader","level":"info","message":"JwtModule dependencies initialized","timestamp":"2025-06-15T09:57:54.487Z"}
{"context":"ElasticsearchOrbitService","level":"info","message":"TLE查询配置加载完成: 时间窗口=30分钟, 最大记录数=50000","timestamp":"2025-06-15T09:57:54.490Z"}
{"context":"InstanceLoader","level":"info","message":"OrbitAnalysisModule dependencies initialized","timestamp":"2025-06-15T09:57:54.491Z"}
{"context":"NewsSchedulerService","level":"info","message":"新闻定时任务服务初始化完成，配置: {\"enabled\":true,\"cronExpression\":\"0 5,12,18 * * *\",\"timezone\":\"Asia/Shanghai\"}","timestamp":"2025-06-15T09:57:54.492Z"}
{"context":"InstanceLoader","level":"info","message":"ESModule dependencies initialized","timestamp":"2025-06-15T09:57:54.493Z"}
{"context":"InstanceLoader","level":"info","message":"NewsSchedulerModule dependencies initialized","timestamp":"2025-06-15T09:57:54.493Z"}
{"context":"InstanceLoader","level":"info","message":"SatelliteTilesModule dependencies initialized","timestamp":"2025-06-15T09:57:54.493Z"}
{"context":"ElasticsearchOrbitService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-06-15T09:57:54.525Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-06-15T09:57:54.525Z"}
{"context":"ElasticsearchDebrisEventService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-06-15T09:57:54.526Z"}
{"context":"ElasticsearchRocketService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-06-15T09:57:54.526Z"}
{"context":"ElasticsearchLaunchService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-06-15T09:57:54.526Z"}
{"context":"ElasticsearchBaseService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-06-15T09:57:54.526Z"}
{"context":"ElasticsearchConstellationService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-06-15T09:57:54.527Z"}
{"context":"ElasticsearchSatelliteService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-06-15T09:57:54.527Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmCoreModule dependencies initialized","timestamp":"2025-06-15T09:57:54.572Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-06-15T09:57:54.573Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-06-15T09:57:54.573Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-06-15T09:57:54.573Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-06-15T09:57:54.573Z"}
{"context":"InstanceLoader","level":"info","message":"UserModule dependencies initialized","timestamp":"2025-06-15T09:57:54.573Z"}
{"context":"InstanceLoader","level":"info","message":"AppModule dependencies initialized","timestamp":"2025-06-15T09:57:54.573Z"}
{"context":"SatelliteSchedulerService","level":"info","message":"卫星数据定时任务服务初始化完成，配置: {\"enabled\":true,\"cronExpression\":\"0 3 * * *\",\"timezone\":\"Asia/Shanghai\",\"saveToDatabase\":true}","timestamp":"2025-06-15T09:57:54.574Z"}
{"context":"InstanceLoader","level":"info","message":"DatabaseModule dependencies initialized","timestamp":"2025-06-15T09:57:54.574Z"}
{"context":"InstanceLoader","level":"info","message":"AggregationTaskModule dependencies initialized","timestamp":"2025-06-15T09:57:54.574Z"}
{"context":"InstanceLoader","level":"info","message":"AuthModule dependencies initialized","timestamp":"2025-06-15T09:57:54.574Z"}
{"context":"InstanceLoader","level":"info","message":"SatelliteModule dependencies initialized","timestamp":"2025-06-15T09:57:54.574Z"}
{"context":"InstanceLoader","level":"info","message":"SatelliteSchedulerModule dependencies initialized","timestamp":"2025-06-15T09:57:54.574Z"}
{"context":"RoutesResolver","level":"info","message":"SatelliteAggregationController {/local/satellite-aggregation}:","timestamp":"2025-06-15T09:57:54.646Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite-aggregation/status, GET} route","timestamp":"2025-06-15T09:57:54.647Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite-aggregation/running, GET} route","timestamp":"2025-06-15T09:57:54.647Z"}
{"context":"RoutesResolver","level":"info","message":"AuthController {/auth}:","timestamp":"2025-06-15T09:57:54.647Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/register, POST} route","timestamp":"2025-06-15T09:57:54.648Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/login, POST} route","timestamp":"2025-06-15T09:57:54.648Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/users/:id/role, PUT} route","timestamp":"2025-06-15T09:57:54.648Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/users/:id/permissions, GET} route","timestamp":"2025-06-15T09:57:54.648Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/users, GET} route","timestamp":"2025-06-15T09:57:54.648Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/profile, GET} route","timestamp":"2025-06-15T09:57:54.648Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/admin/pending-users, GET} route","timestamp":"2025-06-15T09:57:54.648Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/admin/users/:id/approval, PUT} route","timestamp":"2025-06-15T09:57:54.649Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/admin/user-approval-history, GET} route","timestamp":"2025-06-15T09:57:54.649Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/admin/users/:id/delete, DELETE} route","timestamp":"2025-06-15T09:57:54.649Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/change-password, PUT} route","timestamp":"2025-06-15T09:57:54.649Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchDebrisEventController {/api/debris-events}:","timestamp":"2025-06-15T09:57:54.649Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/debris-events/search, POST} route","timestamp":"2025-06-15T09:57:54.649Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchConstellationController {/constellation}:","timestamp":"2025-06-15T09:57:54.649Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/constellation/search, POST} route","timestamp":"2025-06-15T09:57:54.649Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/constellation/names, GET} route","timestamp":"2025-06-15T09:57:54.649Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/constellation/organizations, GET} route","timestamp":"2025-06-15T09:57:54.649Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/constellation/purposes, GET} route","timestamp":"2025-06-15T09:57:54.649Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchCorrelationController {/correlation}:","timestamp":"2025-06-15T09:57:54.649Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/correlation/debris-to-event, POST} route","timestamp":"2025-06-15T09:57:54.650Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/correlation/event-to-debris, POST} route","timestamp":"2025-06-15T09:57:54.650Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchDebrisController {/debris}:","timestamp":"2025-06-15T09:57:54.650Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/search, POST} route","timestamp":"2025-06-15T09:57:54.650Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/names, GET} route","timestamp":"2025-06-15T09:57:54.650Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/object-classes, GET} route","timestamp":"2025-06-15T09:57:54.650Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/missions, GET} route","timestamp":"2025-06-15T09:57:54.650Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/countries, GET} route","timestamp":"2025-06-15T09:57:54.650Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/debug-query-dto, GET} route","timestamp":"2025-06-15T09:57:54.650Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchSatelliteController {/satellite}:","timestamp":"2025-06-15T09:57:54.650Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/satellite/search, POST} route","timestamp":"2025-06-15T09:57:54.650Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/satellite/names, GET} route","timestamp":"2025-06-15T09:57:54.650Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/satellite/statuses, GET} route","timestamp":"2025-06-15T09:57:54.650Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/satellite/test-direct-query, POST} route","timestamp":"2025-06-15T09:57:54.650Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchFreqController {/freq}:","timestamp":"2025-06-15T09:57:54.650Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/freq/search, POST} route","timestamp":"2025-06-15T09:57:54.651Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchOrbitController {/orbit}:","timestamp":"2025-06-15T09:57:54.651Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/search, POST} route","timestamp":"2025-06-15T09:57:54.651Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/bulk-tle, POST} route","timestamp":"2025-06-15T09:57:54.651Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/bulk-tle/all, POST} route","timestamp":"2025-06-15T09:57:54.651Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/tle-config, GET} route","timestamp":"2025-06-15T09:57:54.652Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/tle-config, PUT} route","timestamp":"2025-06-15T09:57:54.652Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchLoopholeController {/loophole}:","timestamp":"2025-06-15T09:57:54.652Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/loophole/search, POST} route","timestamp":"2025-06-15T09:57:54.652Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchNewsController {/api/es/news}:","timestamp":"2025-06-15T09:57:54.652Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/indices, GET} route","timestamp":"2025-06-15T09:57:54.652Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/translation-status, GET} route","timestamp":"2025-06-15T09:57:54.652Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/translate, POST} route","timestamp":"2025-06-15T09:57:54.652Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/api-stats, GET} route","timestamp":"2025-06-15T09:57:54.652Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/failure-stats, GET} route","timestamp":"2025-06-15T09:57:54.652Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/reset-failure-stats, POST} route","timestamp":"2025-06-15T09:57:54.652Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/failed-translations, GET} route","timestamp":"2025-06-15T09:57:54.652Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/retry-failed, POST} route","timestamp":"2025-06-15T09:57:54.653Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/extract-themes, POST} route","timestamp":"2025-06-15T09:57:54.653Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/list, POST} route","timestamp":"2025-06-15T09:57:54.653Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/hot-themes, GET} route","timestamp":"2025-06-15T09:57:54.653Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchLaunchController {/api/es/launch}:","timestamp":"2025-06-15T09:57:54.653Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/search, POST} route","timestamp":"2025-06-15T09:57:54.653Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/rocket-names, GET} route","timestamp":"2025-06-15T09:57:54.653Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/site-names, GET} route","timestamp":"2025-06-15T09:57:54.653Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/providers, GET} route","timestamp":"2025-06-15T09:57:54.653Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/cospar, POST} route","timestamp":"2025-06-15T09:57:54.653Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/wiki-site-names, GET} route","timestamp":"2025-06-15T09:57:54.653Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/all-launch-sites, GET} route","timestamp":"2025-06-15T09:57:54.653Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/wiki-site-info, GET} route","timestamp":"2025-06-15T09:57:54.653Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/service-providers/search, POST} route","timestamp":"2025-06-15T09:57:54.653Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchRocketController {/api/es/rocket}:","timestamp":"2025-06-15T09:57:54.653Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/rocket/search, POST} route","timestamp":"2025-06-15T09:57:54.654Z"}
{"context":"RoutesResolver","level":"info","message":"OrbitCalculatorController {/api/orbit-calculator}:","timestamp":"2025-06-15T09:57:54.654Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/orbit-calculator/calculate-positions, POST} route","timestamp":"2025-06-15T09:57:54.654Z"}
{"context":"RoutesResolver","level":"info","message":"PassAnalysisController {/api/pass-analysis}:","timestamp":"2025-06-15T09:57:54.654Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/pass-analysis/pass-analysis, POST} route","timestamp":"2025-06-15T09:57:54.654Z"}
{"context":"RoutesResolver","level":"info","message":"ConjunctionAnalysisController {/api/conjunction-analysis}:","timestamp":"2025-06-15T09:57:54.654Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/conjunction-analysis/conjunction-analysis, POST} route","timestamp":"2025-06-15T09:57:54.654Z"}
{"context":"RoutesResolver","level":"info","message":"SatelliteController {/local/satellite}:","timestamp":"2025-06-15T09:57:54.654Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/search, POST} route","timestamp":"2025-06-15T09:57:54.654Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/names, GET} route","timestamp":"2025-06-15T09:57:54.654Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/statuses, GET} route","timestamp":"2025-06-15T09:57:54.654Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/sync, POST} route","timestamp":"2025-06-15T09:57:54.654Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/sync-all, POST} route","timestamp":"2025-06-15T09:57:54.655Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/aggregate, POST} route","timestamp":"2025-06-15T09:57:54.655Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/clear, POST} route","timestamp":"2025-06-15T09:57:54.655Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/orbit-classes-local, GET} route","timestamp":"2025-06-15T09:57:54.655Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/orbit-types-local, GET} route","timestamp":"2025-06-15T09:57:54.655Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/:id, GET} route","timestamp":"2025-06-15T09:57:54.655Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite, GET} route","timestamp":"2025-06-15T09:57:54.655Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/test-aggregation, POST} route","timestamp":"2025-06-15T09:57:54.655Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/test-custom-aggregation, POST} route","timestamp":"2025-06-15T09:57:54.655Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/aggregate-all, POST} route","timestamp":"2025-06-15T09:57:54.655Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/incremental-aggregate, POST} route","timestamp":"2025-06-15T09:57:54.655Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/update-orbit-info, POST} route","timestamp":"2025-06-15T09:57:54.655Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/update-users, POST} route","timestamp":"2025-06-15T09:57:54.655Z"}
{"context":"RoutesResolver","level":"info","message":"ConstellationController {/local/constellation}:","timestamp":"2025-06-15T09:57:54.655Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/constellation, GET} route","timestamp":"2025-06-15T09:57:54.655Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/constellation/update, POST} route","timestamp":"2025-06-15T09:57:54.656Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/constellation/with-tle, GET} route","timestamp":"2025-06-15T09:57:54.656Z"}
{"context":"RoutesResolver","level":"info","message":"AggregationTaskController {/tasks/aggregation}:","timestamp":"2025-06-15T09:57:54.656Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation, GET} route","timestamp":"2025-06-15T09:57:54.656Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/latest, GET} route","timestamp":"2025-06-15T09:57:54.656Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/running, GET} route","timestamp":"2025-06-15T09:57:54.656Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id, GET} route","timestamp":"2025-06-15T09:57:54.656Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation, POST} route","timestamp":"2025-06-15T09:57:54.656Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id/start, POST} route","timestamp":"2025-06-15T09:57:54.656Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id/update-progress, POST} route","timestamp":"2025-06-15T09:57:54.657Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id/complete, POST} route","timestamp":"2025-06-15T09:57:54.657Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id/fail, POST} route","timestamp":"2025-06-15T09:57:54.657Z"}
{"context":"RoutesResolver","level":"info","message":"DatabaseController {/api/v1/database}:","timestamp":"2025-06-15T09:57:54.657Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/query, POST} route","timestamp":"2025-06-15T09:57:54.657Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/filter-satellites, POST} route","timestamp":"2025-06-15T09:57:54.657Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/satellite-users, GET} route","timestamp":"2025-06-15T09:57:54.657Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/satellite-purposes, GET} route","timestamp":"2025-06-15T09:57:54.657Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/launch-contractors, GET} route","timestamp":"2025-06-15T09:57:54.657Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/launch-sites, GET} route","timestamp":"2025-06-15T09:57:54.657Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/launch-vehicles, GET} route","timestamp":"2025-06-15T09:57:54.657Z"}
{"context":"RoutesResolver","level":"info","message":"SatelliteTilesController {/tiles}:","timestamp":"2025-06-15T09:57:54.657Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tiles/generate, POST} route","timestamp":"2025-06-15T09:57:54.658Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tiles/status, GET} route","timestamp":"2025-06-15T09:57:54.658Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tiles/satellites, GET} route","timestamp":"2025-06-15T09:57:54.658Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tiles/metadata, GET} route","timestamp":"2025-06-15T09:57:54.658Z"}
{"context":"RoutesResolver","level":"info","message":"NewsSchedulerController {/api/news-scheduler}:","timestamp":"2025-06-15T09:57:54.658Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/status, GET} route","timestamp":"2025-06-15T09:57:54.658Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/trigger, POST} route","timestamp":"2025-06-15T09:57:54.658Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/config, POST} route","timestamp":"2025-06-15T09:57:54.658Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/stop, POST} route","timestamp":"2025-06-15T09:57:54.658Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/cron-expressions, GET} route","timestamp":"2025-06-15T09:57:54.658Z"}
{"context":"RoutesResolver","level":"info","message":"SatelliteSchedulerController {/api/satellite-scheduler}:","timestamp":"2025-06-15T09:57:54.658Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/satellite-scheduler/status, GET} route","timestamp":"2025-06-15T09:57:54.659Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/satellite-scheduler/trigger, POST} route","timestamp":"2025-06-15T09:57:54.659Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/satellite-scheduler/config, POST} route","timestamp":"2025-06-15T09:57:54.659Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/satellite-scheduler/stop, POST} route","timestamp":"2025-06-15T09:57:54.659Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/satellite-scheduler/cron-expressions, GET} route","timestamp":"2025-06-15T09:57:54.659Z"}
{"context":"DatabaseService","level":"info","message":"正在检查并启用pg_trgm扩展...","timestamp":"2025-06-15T09:57:54.665Z"}
{"context":"DatabaseService","level":"info","message":"pg_trgm扩展已启用","timestamp":"2025-06-15T09:57:54.668Z"}
{"context":"NestApplication","level":"info","message":"Nest application successfully started","timestamp":"2025-06-15T09:57:54.679Z"}
{"context":"NewsSchedulerService","level":"info","message":"🕐 新闻处理定时任务触发：开始执行新闻翻译和主题提取","timestamp":"2025-06-15T10:00:00.049Z"}
{"context":"NewsSchedulerService","level":"info","message":"🚀 开始执行新闻处理流水线","timestamp":"2025-06-15T10:00:00.052Z"}
{"context":"NewsSchedulerService","level":"info","message":"📝 开始执行新闻翻译和主题提取任务（优化版一次性调用）","timestamp":"2025-06-15T10:00:00.052Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"使用模式: default, 模型: qwen-turbo","timestamp":"2025-06-15T10:00:00.089Z"}
{"context":"TranslationService","level":"info","message":"翻译配置已更新 - 模型: qwen-turbo","timestamp":"2025-06-15T10:00:00.090Z"}
{"context":"NewsSchedulerService","level":"warn","message":"新闻处理任务正在运行中，跳过本次执行","timestamp":"2025-06-15T10:00:00.133Z"}
{"context":"NewsSchedulerService","level":"warn","message":"新闻处理任务正在运行中，跳过本次执行","timestamp":"2025-06-15T10:00:00.244Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"找到12个新闻索引","timestamp":"2025-06-15T10:00:00.295Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"开始翻译12个新闻索引的数据，批次大小: 20, 强制重翻译: false, 强制重新爬取: false, 自动提取主题词: true","timestamp":"2025-06-15T10:00:00.295Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_clearspace, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:00:00.297Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_clearspace获取了0条未翻译的新闻","timestamp":"2025-06-15T10:00:00.314Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_defenseone, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:00:00.314Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_defenseone获取了0条未翻译的新闻","timestamp":"2025-06-15T10:00:00.325Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:00:00.325Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:00:00.354Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"找到12个新闻索引","timestamp":"2025-06-15T10:00:11.851Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"找到12个新闻索引","timestamp":"2025-06-15T10:00:11.862Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"找到12个新闻索引","timestamp":"2025-06-15T10:00:12.216Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"找到12个新闻索引","timestamp":"2025-06-15T10:00:12.216Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"找到12个新闻索引","timestamp":"2025-06-15T10:00:12.348Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"找到12个新闻索引","timestamp":"2025-06-15T10:00:12.376Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"找到12个新闻索引","timestamp":"2025-06-15T10:00:13.231Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"找到12个新闻索引","timestamp":"2025-06-15T10:00:13.232Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"找到12个新闻索引","timestamp":"2025-06-15T10:01:24.774Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"找到12个新闻索引","timestamp":"2025-06-15T10:01:24.790Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"找到12个新闻索引","timestamp":"2025-06-15T10:01:25.301Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"找到12个新闻索引","timestamp":"2025-06-15T10:01:25.302Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"找到12个新闻索引","timestamp":"2025-06-15T10:01:25.469Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"找到12个新闻索引","timestamp":"2025-06-15T10:01:25.471Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"找到12个新闻索引","timestamp":"2025-06-15T10:01:27.942Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"找到12个新闻索引","timestamp":"2025-06-15T10:01:27.947Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:01:36.416Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:01:36.450Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"找到12个新闻索引","timestamp":"2025-06-15T10:02:06.055Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"找到12个新闻索引","timestamp":"2025-06-15T10:02:06.063Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"找到12个新闻索引","timestamp":"2025-06-15T10:02:06.216Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"找到12个新闻索引","timestamp":"2025-06-15T10:02:06.231Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"找到12个新闻索引","timestamp":"2025-06-15T10:02:06.373Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"找到12个新闻索引","timestamp":"2025-06-15T10:02:06.374Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"找到12个新闻索引","timestamp":"2025-06-15T10:02:08.196Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"找到12个新闻索引","timestamp":"2025-06-15T10:02:08.196Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:03:06.413Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:03:07.123Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"找到12个新闻索引","timestamp":"2025-06-15T10:03:20.341Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"找到12个新闻索引","timestamp":"2025-06-15T10:03:20.343Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"找到12个新闻索引","timestamp":"2025-06-15T10:03:20.480Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"找到12个新闻索引","timestamp":"2025-06-15T10:03:20.496Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"找到12个新闻索引","timestamp":"2025-06-15T10:03:20.615Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"找到12个新闻索引","timestamp":"2025-06-15T10:03:20.633Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"找到12个新闻索引","timestamp":"2025-06-15T10:03:22.865Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"找到12个新闻索引","timestamp":"2025-06-15T10:03:22.880Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:04:33.587Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:04:33.933Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"找到12个新闻索引","timestamp":"2025-06-15T10:05:24.214Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"找到12个新闻索引","timestamp":"2025-06-15T10:05:24.262Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"找到12个新闻索引","timestamp":"2025-06-15T10:05:24.499Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"找到12个新闻索引","timestamp":"2025-06-15T10:05:24.515Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"找到12个新闻索引","timestamp":"2025-06-15T10:05:24.603Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"找到12个新闻索引","timestamp":"2025-06-15T10:05:24.617Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"找到12个新闻索引","timestamp":"2025-06-15T10:05:26.128Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"找到12个新闻索引","timestamp":"2025-06-15T10:05:26.128Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"找到12个新闻索引","timestamp":"2025-06-15T10:05:40.751Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"找到12个新闻索引","timestamp":"2025-06-15T10:05:40.756Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"找到12个新闻索引","timestamp":"2025-06-15T10:05:40.965Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"找到12个新闻索引","timestamp":"2025-06-15T10:05:40.971Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"找到12个新闻索引","timestamp":"2025-06-15T10:05:41.147Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"找到12个新闻索引","timestamp":"2025-06-15T10:05:41.147Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"找到12个新闻索引","timestamp":"2025-06-15T10:05:42.756Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"找到12个新闻索引","timestamp":"2025-06-15T10:05:42.757Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:05:48.729Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:05:48.769Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:06:58.366Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:06:59.131Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:08:05.648Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:08:05.745Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:09:31.464Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:09:32.483Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:10:41.208Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:10:42.073Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:10:44.390Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:10:44.415Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:10:46.710Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:10:46.739Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:10:49.040Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:10:49.060Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:10:51.345Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:10:51.371Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:10:53.736Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:10:53.770Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:10:56.104Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:10:56.131Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:10:58.437Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:10:58.462Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:11:00.778Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:11:00.804Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:11:03.137Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:11:03.161Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:11:05.464Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:11:05.486Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:11:07.801Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:11:07.825Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:11:10.091Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:11:10.114Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:11:12.408Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:11:12.430Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:11:14.734Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:11:14.761Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:11:17.077Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:11:17.102Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:11:19.397Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:11:19.424Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:11:21.728Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:11:21.758Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:11:24.071Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:11:24.094Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:11:26.401Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:11:26.424Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:11:28.718Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:11:28.753Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:11:31.031Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:11:31.068Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:11:33.352Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:11:33.392Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:11:35.691Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:11:35.727Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:11:38.031Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:11:38.070Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:11:40.373Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:11:40.408Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:11:42.692Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:11:42.734Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:11:45.033Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:11:45.069Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:11:47.374Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:11:47.399Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:11:49.840Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:11:49.877Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:11:52.233Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:11:52.282Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:11:54.584Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:11:54.606Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:11:56.917Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:11:56.938Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:11:59.223Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:11:59.246Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:12:01.547Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:12:01.574Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:12:03.846Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:12:03.871Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:12:06.132Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:12:06.153Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:12:08.438Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:12:08.461Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:12:10.744Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:12:10.769Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:12:13.147Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:12:13.169Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:12:15.466Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:12:15.497Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:12:17.776Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:12:17.800Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:12:20.107Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:12:20.134Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:12:22.448Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:12:22.469Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:12:24.755Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:12:24.780Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:12:27.084Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:12:27.113Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:12:29.399Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:12:29.449Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:12:31.853Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:12:31.875Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:12:34.159Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:12:34.181Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:12:36.469Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:12:36.500Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:12:38.779Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:12:38.802Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:12:41.081Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:12:41.107Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:12:43.402Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:12:43.428Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:12:45.715Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:12:45.740Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:12:48.034Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:12:48.061Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:12:50.372Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:12:50.399Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:12:52.689Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:12:52.716Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:12:55.006Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:12:55.033Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:12:57.321Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:12:57.344Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:12:59.624Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:12:59.648Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:13:01.934Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:13:01.957Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:13:04.264Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:13:04.288Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:13:06.581Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:13:06.602Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:13:08.900Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:13:08.934Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:13:11.214Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:13:11.246Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:13:13.533Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:13:13.560Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:13:15.848Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:13:15.872Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:13:18.171Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:13:18.195Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:13:20.480Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:13:20.507Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:13:22.798Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:13:22.822Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:13:25.111Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:13:25.143Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:13:27.420Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:13:27.446Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:13:29.739Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:13:29.763Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:13:32.054Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:13:32.081Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:13:34.357Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:13:34.379Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:13:36.740Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:13:36.766Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:13:39.070Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:13:39.100Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:13:41.396Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:13:41.418Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:13:43.698Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:13:43.720Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:13:46.010Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:13:46.038Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:13:48.323Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:13:48.345Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:13:50.640Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:13:50.667Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:13:52.942Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:13:52.967Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:13:55.251Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:13:55.276Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:13:57.567Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:13:57.592Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:13:59.879Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:13:59.901Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:14:02.214Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:14:02.236Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:14:04.516Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:14:04.539Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:14:06.825Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:14:06.850Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:14:09.131Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:14:09.155Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:14:11.447Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:14:11.469Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:14:13.770Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:14:13.794Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:14:16.082Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:14:16.105Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:14:18.390Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:14:18.413Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:14:20.723Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:14:20.747Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:14:23.023Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:14:23.047Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:14:25.426Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:14:25.447Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:14:27.722Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:14:27.751Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:14:30.038Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:14:30.063Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:14:32.355Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:14:32.382Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:14:34.669Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:14:34.697Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:14:36.987Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:14:37.010Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:14:39.313Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:14:39.336Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:14:41.622Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:14:41.648Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:14:43.947Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:14:43.975Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:14:46.289Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:14:46.319Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:14:48.602Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:14:48.627Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:14:50.912Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:14:50.936Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:14:53.227Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:14:53.252Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:14:55.557Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:14:55.584Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:14:57.900Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:14:57.924Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:15:00.229Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:15:00.254Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:15:02.603Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:15:02.627Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:15:04.907Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:15:04.930Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:15:07.204Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:15:07.228Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:15:09.525Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:15:09.548Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:15:11.834Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:15:11.868Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:15:14.140Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:15:14.164Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:15:16.433Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:15:16.455Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:15:18.741Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:15:18.764Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:15:21.059Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:15:21.084Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:15:23.380Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:15:23.402Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:15:25.677Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:15:25.699Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:15:27.976Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:15:27.998Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:15:30.278Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:15:30.303Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:15:32.608Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:15:32.643Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:15:34.920Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:15:34.955Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:15:37.273Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:15:37.311Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:15:39.602Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:15:39.640Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:15:41.937Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:15:41.962Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:15:44.245Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:15:44.270Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:15:46.571Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:15:46.598Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:15:48.886Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:15:48.909Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:15:51.212Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:15:51.240Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:15:53.532Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:15:53.556Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:15:55.836Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:15:55.858Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:15:58.154Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:15:58.176Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:16:00.450Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:16:00.476Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:16:02.754Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:16:02.782Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:16:05.070Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:16:05.095Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:16:07.396Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:16:07.429Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:16:09.716Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:16:09.739Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:16:12.038Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:16:12.062Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:16:14.347Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:16:14.370Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:16:16.653Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:16:16.677Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:16:18.963Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:16:18.989Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:16:21.274Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:16:21.297Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:16:23.586Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:16:23.608Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:16:25.886Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:16:25.910Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:16:28.192Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:16:28.214Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:16:30.508Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:16:30.535Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:16:32.827Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:16:32.854Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:16:35.126Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:16:35.149Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:16:37.437Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:16:37.465Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:16:39.750Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:16:39.774Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:16:42.071Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:16:42.096Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:16:44.385Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:16:44.428Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:16:46.716Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:16:46.749Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:16:49.042Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:16:49.067Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:16:51.357Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:16:51.379Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:16:53.649Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:16:53.671Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:16:55.960Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:16:55.983Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:16:58.280Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:16:58.307Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:17:00.613Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:17:00.635Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:17:02.921Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:17:02.943Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:17:05.252Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:17:05.279Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:17:07.567Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:17:07.592Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:17:09.871Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:17:09.895Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:17:12.192Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:17:12.215Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:17:14.492Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:17:14.516Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:17:16.798Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:17:16.822Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:17:19.137Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:17:19.163Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:17:21.440Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:17:21.464Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:17:23.749Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:17:23.773Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:17:26.113Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:17:26.136Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:17:28.425Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:17:28.448Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:17:30.734Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:17:30.757Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:17:33.041Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:17:33.065Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:17:35.358Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:17:35.385Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:17:37.669Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:17:37.690Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:17:39.986Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:17:40.012Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:17:42.320Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:17:42.347Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:17:44.652Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:17:44.687Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:17:46.987Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:17:47.014Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:17:49.317Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:17:49.347Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:17:51.657Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:17:51.682Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:17:53.971Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:17:53.997Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:17:56.294Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:17:56.322Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:17:58.620Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:17:58.643Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:18:00.930Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:18:00.956Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:18:03.238Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:18:03.261Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:18:05.539Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:18:05.564Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:18:07.865Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:18:07.891Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:18:10.183Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:18:10.206Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:18:12.499Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:18:12.538Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:18:14.828Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:18:14.852Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:18:17.150Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:18:17.173Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:18:19.443Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:18:19.468Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:18:21.756Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:18:21.786Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:18:24.054Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:18:24.077Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:18:26.389Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:18:26.414Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:18:28.695Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:18:28.717Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:18:31.016Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:18:31.039Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:18:33.320Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:18:33.344Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:18:35.632Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:18:35.657Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:18:37.943Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:18:37.968Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:18:40.257Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:18:40.280Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:18:42.564Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:18:42.588Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:18:44.890Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:18:44.915Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:18:47.230Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:18:47.252Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:18:49.555Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:18:49.582Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:18:51.896Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:18:51.923Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:18:54.270Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:18:54.292Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:18:56.582Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:18:56.617Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:18:58.932Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:18:58.971Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:19:01.288Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:19:01.318Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:19:03.611Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:19:03.637Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:19:05.958Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:19:05.984Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:19:08.274Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:19:08.301Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:19:10.618Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:19:10.647Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:19:12.983Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:19:13.019Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:19:15.373Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:19:15.402Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:19:17.748Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:19:17.796Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:19:20.087Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:19:20.120Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:19:22.426Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:19:22.451Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:19:24.750Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:19:24.782Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:19:27.086Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:19:27.111Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:19:29.403Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:19:29.426Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:19:31.711Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:19:31.734Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:19:34.008Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:19:34.032Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"获取未翻译的新闻，索引=news_thespacedevs, 批次大小=20, 强制重新翻译=false","timestamp":"2025-06-15T10:19:36.322Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"从索引news_thespacedevs获取了20条未翻译的新闻","timestamp":"2025-06-15T10:19:36.346Z"}
{"context":"NestFactory","level":"info","message":"Starting Nest application...","timestamp":"2025-06-15T10:19:39.667Z"}
{"context":"TranslationService","level":"info","message":"加载翻译配置 - 模式: default, 模型: qwen-turbo","timestamp":"2025-06-15T10:19:39.685Z"}
{"context":"TranslationService","level":"info","message":"加载主题提取配置 - 模式: default, 模型: qwen-turbo","timestamp":"2025-06-15T10:19:39.685Z"}
{"context":"TranslationService","level":"info","message":"翻译服务初始化完成 - 翻译模型: qwen-turbo, 主题提取模型: qwen-turbo","timestamp":"2025-06-15T10:19:39.686Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-06-15T10:19:39.687Z"}
{"context":"InstanceLoader","level":"info","message":"PassportModule dependencies initialized","timestamp":"2025-06-15T10:19:39.687Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-06-15T10:19:39.687Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigHostModule dependencies initialized","timestamp":"2025-06-15T10:19:39.688Z"}
{"context":"InstanceLoader","level":"info","message":"DiscoveryModule dependencies initialized","timestamp":"2025-06-15T10:19:39.688Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-06-15T10:19:39.688Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-06-15T10:19:39.689Z"}
{"context":"InstanceLoader","level":"info","message":"ScheduleModule dependencies initialized","timestamp":"2025-06-15T10:19:39.689Z"}
{"context":"InstanceLoader","level":"info","message":"ScheduleModule dependencies initialized","timestamp":"2025-06-15T10:19:39.689Z"}
{"context":"InstanceLoader","level":"info","message":"ScheduleModule dependencies initialized","timestamp":"2025-06-15T10:19:39.689Z"}
{"context":"InstanceLoader","level":"info","message":"ElasticsearchModule dependencies initialized","timestamp":"2025-06-15T10:19:39.691Z"}
{"context":"InstanceLoader","level":"info","message":"JwtModule dependencies initialized","timestamp":"2025-06-15T10:19:39.691Z"}
{"context":"ElasticsearchOrbitService","level":"info","message":"TLE查询配置加载完成: 时间窗口=30分钟, 最大记录数=50000","timestamp":"2025-06-15T10:19:39.694Z"}
{"context":"InstanceLoader","level":"info","message":"OrbitAnalysisModule dependencies initialized","timestamp":"2025-06-15T10:19:39.695Z"}
{"context":"NewsSchedulerService","level":"info","message":"新闻定时任务服务初始化完成，配置: {\"enabled\":true,\"cronExpression\":\"0 5,12,18 * * *\",\"timezone\":\"Asia/Shanghai\"}","timestamp":"2025-06-15T10:19:39.696Z"}
{"context":"InstanceLoader","level":"info","message":"ESModule dependencies initialized","timestamp":"2025-06-15T10:19:39.696Z"}
{"context":"InstanceLoader","level":"info","message":"NewsSchedulerModule dependencies initialized","timestamp":"2025-06-15T10:19:39.697Z"}
{"context":"InstanceLoader","level":"info","message":"SatelliteTilesModule dependencies initialized","timestamp":"2025-06-15T10:19:39.697Z"}
{"context":"ElasticsearchConstellationService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-06-15T10:19:39.725Z"}
{"context":"ElasticsearchRocketService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-06-15T10:19:39.725Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-06-15T10:19:39.725Z"}
{"context":"ElasticsearchBaseService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-06-15T10:19:39.725Z"}
{"context":"ElasticsearchSatelliteService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-06-15T10:19:39.725Z"}
{"context":"ElasticsearchDebrisEventService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-06-15T10:19:39.725Z"}
{"context":"ElasticsearchOrbitService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-06-15T10:19:39.726Z"}
{"context":"ElasticsearchLaunchService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-06-15T10:19:39.726Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmCoreModule dependencies initialized","timestamp":"2025-06-15T10:19:39.769Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-06-15T10:19:39.769Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-06-15T10:19:39.769Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-06-15T10:19:39.769Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-06-15T10:19:39.769Z"}
{"context":"InstanceLoader","level":"info","message":"UserModule dependencies initialized","timestamp":"2025-06-15T10:19:39.770Z"}
{"context":"InstanceLoader","level":"info","message":"AppModule dependencies initialized","timestamp":"2025-06-15T10:19:39.770Z"}
{"context":"SatelliteSchedulerService","level":"info","message":"卫星数据定时任务服务初始化完成，配置: {\"enabled\":true,\"cronExpression\":\"0 3 * * *\",\"timezone\":\"Asia/Shanghai\",\"saveToDatabase\":true}","timestamp":"2025-06-15T10:19:39.770Z"}
{"context":"InstanceLoader","level":"info","message":"DatabaseModule dependencies initialized","timestamp":"2025-06-15T10:19:39.771Z"}
{"context":"InstanceLoader","level":"info","message":"AggregationTaskModule dependencies initialized","timestamp":"2025-06-15T10:19:39.771Z"}
{"context":"InstanceLoader","level":"info","message":"AuthModule dependencies initialized","timestamp":"2025-06-15T10:19:39.771Z"}
{"context":"InstanceLoader","level":"info","message":"SatelliteModule dependencies initialized","timestamp":"2025-06-15T10:19:39.771Z"}
{"context":"InstanceLoader","level":"info","message":"SatelliteSchedulerModule dependencies initialized","timestamp":"2025-06-15T10:19:39.771Z"}
{"context":"RoutesResolver","level":"info","message":"SatelliteAggregationController {/local/satellite-aggregation}:","timestamp":"2025-06-15T10:19:39.833Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite-aggregation/status, GET} route","timestamp":"2025-06-15T10:19:39.834Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite-aggregation/running, GET} route","timestamp":"2025-06-15T10:19:39.834Z"}
{"context":"RoutesResolver","level":"info","message":"AuthController {/auth}:","timestamp":"2025-06-15T10:19:39.834Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/register, POST} route","timestamp":"2025-06-15T10:19:39.835Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/login, POST} route","timestamp":"2025-06-15T10:19:39.835Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/users/:id/role, PUT} route","timestamp":"2025-06-15T10:19:39.835Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/users/:id/permissions, GET} route","timestamp":"2025-06-15T10:19:39.835Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/users, GET} route","timestamp":"2025-06-15T10:19:39.835Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/profile, GET} route","timestamp":"2025-06-15T10:19:39.835Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/admin/pending-users, GET} route","timestamp":"2025-06-15T10:19:39.835Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/admin/users/:id/approval, PUT} route","timestamp":"2025-06-15T10:19:39.836Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/admin/user-approval-history, GET} route","timestamp":"2025-06-15T10:19:39.836Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/admin/users/:id/delete, DELETE} route","timestamp":"2025-06-15T10:19:39.836Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/change-password, PUT} route","timestamp":"2025-06-15T10:19:39.836Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchDebrisEventController {/api/debris-events}:","timestamp":"2025-06-15T10:19:39.836Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/debris-events/search, POST} route","timestamp":"2025-06-15T10:19:39.836Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchConstellationController {/constellation}:","timestamp":"2025-06-15T10:19:39.836Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/constellation/search, POST} route","timestamp":"2025-06-15T10:19:39.836Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/constellation/names, GET} route","timestamp":"2025-06-15T10:19:39.836Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/constellation/organizations, GET} route","timestamp":"2025-06-15T10:19:39.836Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/constellation/purposes, GET} route","timestamp":"2025-06-15T10:19:39.836Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchCorrelationController {/correlation}:","timestamp":"2025-06-15T10:19:39.836Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/correlation/debris-to-event, POST} route","timestamp":"2025-06-15T10:19:39.837Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/correlation/event-to-debris, POST} route","timestamp":"2025-06-15T10:19:39.837Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchDebrisController {/debris}:","timestamp":"2025-06-15T10:19:39.837Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/search, POST} route","timestamp":"2025-06-15T10:19:39.837Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/names, GET} route","timestamp":"2025-06-15T10:19:39.837Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/object-classes, GET} route","timestamp":"2025-06-15T10:19:39.837Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/missions, GET} route","timestamp":"2025-06-15T10:19:39.837Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/countries, GET} route","timestamp":"2025-06-15T10:19:39.837Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/debug-query-dto, GET} route","timestamp":"2025-06-15T10:19:39.837Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchSatelliteController {/satellite}:","timestamp":"2025-06-15T10:19:39.837Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/satellite/search, POST} route","timestamp":"2025-06-15T10:19:39.837Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/satellite/names, GET} route","timestamp":"2025-06-15T10:19:39.837Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/satellite/statuses, GET} route","timestamp":"2025-06-15T10:19:39.837Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/satellite/test-direct-query, POST} route","timestamp":"2025-06-15T10:19:39.837Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchFreqController {/freq}:","timestamp":"2025-06-15T10:19:39.838Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/freq/search, POST} route","timestamp":"2025-06-15T10:19:39.838Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchOrbitController {/orbit}:","timestamp":"2025-06-15T10:19:39.838Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/search, POST} route","timestamp":"2025-06-15T10:19:39.838Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/bulk-tle, POST} route","timestamp":"2025-06-15T10:19:39.838Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/bulk-tle/all, POST} route","timestamp":"2025-06-15T10:19:39.838Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/tle-config, GET} route","timestamp":"2025-06-15T10:19:39.838Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/tle-config, PUT} route","timestamp":"2025-06-15T10:19:39.838Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchLoopholeController {/loophole}:","timestamp":"2025-06-15T10:19:39.838Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/loophole/search, POST} route","timestamp":"2025-06-15T10:19:39.838Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchNewsController {/api/es/news}:","timestamp":"2025-06-15T10:19:39.839Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/indices, GET} route","timestamp":"2025-06-15T10:19:39.839Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/translation-status, GET} route","timestamp":"2025-06-15T10:19:39.839Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/translate, POST} route","timestamp":"2025-06-15T10:19:39.839Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/api-stats, GET} route","timestamp":"2025-06-15T10:19:39.839Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/failure-stats, GET} route","timestamp":"2025-06-15T10:19:39.839Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/reset-failure-stats, POST} route","timestamp":"2025-06-15T10:19:39.839Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/failed-translations, GET} route","timestamp":"2025-06-15T10:19:39.839Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/retry-failed, POST} route","timestamp":"2025-06-15T10:19:39.839Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/extract-themes, POST} route","timestamp":"2025-06-15T10:19:39.839Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/list, POST} route","timestamp":"2025-06-15T10:19:39.839Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/hot-themes, GET} route","timestamp":"2025-06-15T10:19:39.839Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchLaunchController {/api/es/launch}:","timestamp":"2025-06-15T10:19:39.840Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/search, POST} route","timestamp":"2025-06-15T10:19:39.840Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/rocket-names, GET} route","timestamp":"2025-06-15T10:19:39.840Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/site-names, GET} route","timestamp":"2025-06-15T10:19:39.840Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/providers, GET} route","timestamp":"2025-06-15T10:19:39.840Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/cospar, POST} route","timestamp":"2025-06-15T10:19:39.840Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/wiki-site-names, GET} route","timestamp":"2025-06-15T10:19:39.840Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/all-launch-sites, GET} route","timestamp":"2025-06-15T10:19:39.840Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/wiki-site-info, GET} route","timestamp":"2025-06-15T10:19:39.840Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/service-providers/search, POST} route","timestamp":"2025-06-15T10:19:39.840Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchRocketController {/api/es/rocket}:","timestamp":"2025-06-15T10:19:39.840Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/rocket/search, POST} route","timestamp":"2025-06-15T10:19:39.840Z"}
{"context":"RoutesResolver","level":"info","message":"OrbitCalculatorController {/api/orbit-calculator}:","timestamp":"2025-06-15T10:19:39.840Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/orbit-calculator/calculate-positions, POST} route","timestamp":"2025-06-15T10:19:39.840Z"}
{"context":"RoutesResolver","level":"info","message":"PassAnalysisController {/api/pass-analysis}:","timestamp":"2025-06-15T10:19:39.840Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/pass-analysis/pass-analysis, POST} route","timestamp":"2025-06-15T10:19:39.841Z"}
{"context":"RoutesResolver","level":"info","message":"ConjunctionAnalysisController {/api/conjunction-analysis}:","timestamp":"2025-06-15T10:19:39.841Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/conjunction-analysis/conjunction-analysis, POST} route","timestamp":"2025-06-15T10:19:39.841Z"}
{"context":"RoutesResolver","level":"info","message":"SatelliteController {/local/satellite}:","timestamp":"2025-06-15T10:19:39.841Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/search, POST} route","timestamp":"2025-06-15T10:19:39.841Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/names, GET} route","timestamp":"2025-06-15T10:19:39.841Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/statuses, GET} route","timestamp":"2025-06-15T10:19:39.841Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/sync, POST} route","timestamp":"2025-06-15T10:19:39.841Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/sync-all, POST} route","timestamp":"2025-06-15T10:19:39.841Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/aggregate, POST} route","timestamp":"2025-06-15T10:19:39.841Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/clear, POST} route","timestamp":"2025-06-15T10:19:39.841Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/orbit-classes-local, GET} route","timestamp":"2025-06-15T10:19:39.841Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/orbit-types-local, GET} route","timestamp":"2025-06-15T10:19:39.841Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/:id, GET} route","timestamp":"2025-06-15T10:19:39.841Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite, GET} route","timestamp":"2025-06-15T10:19:39.841Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/test-aggregation, POST} route","timestamp":"2025-06-15T10:19:39.841Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/test-custom-aggregation, POST} route","timestamp":"2025-06-15T10:19:39.842Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/aggregate-all, POST} route","timestamp":"2025-06-15T10:19:39.842Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/incremental-aggregate, POST} route","timestamp":"2025-06-15T10:19:39.842Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/update-orbit-info, POST} route","timestamp":"2025-06-15T10:19:39.842Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/update-users, POST} route","timestamp":"2025-06-15T10:19:39.842Z"}
{"context":"RoutesResolver","level":"info","message":"ConstellationController {/local/constellation}:","timestamp":"2025-06-15T10:19:39.842Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/constellation, GET} route","timestamp":"2025-06-15T10:19:39.842Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/constellation/update, POST} route","timestamp":"2025-06-15T10:19:39.842Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/constellation/with-tle, GET} route","timestamp":"2025-06-15T10:19:39.842Z"}
{"context":"RoutesResolver","level":"info","message":"AggregationTaskController {/tasks/aggregation}:","timestamp":"2025-06-15T10:19:39.842Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation, GET} route","timestamp":"2025-06-15T10:19:39.842Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/latest, GET} route","timestamp":"2025-06-15T10:19:39.842Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/running, GET} route","timestamp":"2025-06-15T10:19:39.842Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id, GET} route","timestamp":"2025-06-15T10:19:39.842Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation, POST} route","timestamp":"2025-06-15T10:19:39.842Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id/start, POST} route","timestamp":"2025-06-15T10:19:39.842Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id/update-progress, POST} route","timestamp":"2025-06-15T10:19:39.842Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id/complete, POST} route","timestamp":"2025-06-15T10:19:39.843Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id/fail, POST} route","timestamp":"2025-06-15T10:19:39.843Z"}
{"context":"RoutesResolver","level":"info","message":"DatabaseController {/api/v1/database}:","timestamp":"2025-06-15T10:19:39.843Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/query, POST} route","timestamp":"2025-06-15T10:19:39.843Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/filter-satellites, POST} route","timestamp":"2025-06-15T10:19:39.843Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/satellite-users, GET} route","timestamp":"2025-06-15T10:19:39.843Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/satellite-purposes, GET} route","timestamp":"2025-06-15T10:19:39.843Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/launch-contractors, GET} route","timestamp":"2025-06-15T10:19:39.843Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/launch-sites, GET} route","timestamp":"2025-06-15T10:19:39.844Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/launch-vehicles, GET} route","timestamp":"2025-06-15T10:19:39.844Z"}
{"context":"RoutesResolver","level":"info","message":"SatelliteTilesController {/tiles}:","timestamp":"2025-06-15T10:19:39.844Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tiles/generate, POST} route","timestamp":"2025-06-15T10:19:39.844Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tiles/status, GET} route","timestamp":"2025-06-15T10:19:39.844Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tiles/satellites, GET} route","timestamp":"2025-06-15T10:19:39.844Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tiles/metadata, GET} route","timestamp":"2025-06-15T10:19:39.844Z"}
{"context":"RoutesResolver","level":"info","message":"NewsSchedulerController {/api/news-scheduler}:","timestamp":"2025-06-15T10:19:39.844Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/status, GET} route","timestamp":"2025-06-15T10:19:39.844Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/trigger, POST} route","timestamp":"2025-06-15T10:19:39.844Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/config, POST} route","timestamp":"2025-06-15T10:19:39.845Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/stop, POST} route","timestamp":"2025-06-15T10:19:39.845Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/cron-expressions, GET} route","timestamp":"2025-06-15T10:19:39.845Z"}
{"context":"RoutesResolver","level":"info","message":"SatelliteSchedulerController {/api/satellite-scheduler}:","timestamp":"2025-06-15T10:19:39.845Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/satellite-scheduler/status, GET} route","timestamp":"2025-06-15T10:19:39.845Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/satellite-scheduler/trigger, POST} route","timestamp":"2025-06-15T10:19:39.845Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/satellite-scheduler/config, POST} route","timestamp":"2025-06-15T10:19:39.845Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/satellite-scheduler/stop, POST} route","timestamp":"2025-06-15T10:19:39.845Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/satellite-scheduler/cron-expressions, GET} route","timestamp":"2025-06-15T10:19:39.846Z"}
{"context":"DatabaseService","level":"info","message":"正在检查并启用pg_trgm扩展...","timestamp":"2025-06-15T10:19:39.851Z"}
{"context":"DatabaseService","level":"info","message":"pg_trgm扩展已启用","timestamp":"2025-06-15T10:19:39.857Z"}
{"context":"NestApplication","level":"info","message":"Nest application successfully started","timestamp":"2025-06-15T10:19:39.868Z"}
