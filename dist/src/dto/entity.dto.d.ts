import { EntityType } from '../types/entity.types';
export declare class EntityHighlightDto {
    text: string;
    language?: 'en' | 'cn' | 'auto';
    wholeWordOnly?: boolean;
    caseSensitive?: boolean;
    includeAliases?: boolean;
    includeTranslations?: boolean;
    minConfidence?: number;
    maxMatches?: number;
    entityTypes?: EntityType[];
}
export declare class EntitySearchDto {
    keyword?: string;
    type?: EntityType;
    includeAliases?: boolean;
    includeTranslations?: boolean;
    limit?: number;
    offset?: number;
}
export declare class EntitySyncDto {
    entityTypes?: EntityType[];
    forceSync?: boolean;
}
