"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EntitySyncDto = exports.EntitySearchDto = exports.EntityHighlightDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const entity_types_1 = require("../types/entity.types");
class EntityHighlightDto {
    constructor() {
        this.language = 'auto';
        this.wholeWordOnly = true;
        this.caseSensitive = false;
        this.includeAliases = true;
        this.includeTranslations = true;
        this.minConfidence = 0.8;
    }
}
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '需要进行实体高亮的文本',
        example: 'SpaceX launched Falcon 9 rocket carrying Starlink satellites to orbit.'
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], EntityHighlightDto.prototype, "text", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '文本语言',
        enum: ['en', 'cn', 'auto'],
        required: false,
        default: 'auto',
        example: 'en'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(['en', 'cn', 'auto']),
    __metadata("design:type", String)
], EntityHighlightDto.prototype, "language", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '是否只匹配完整单词',
        required: false,
        default: true,
        example: true
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], EntityHighlightDto.prototype, "wholeWordOnly", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '是否区分大小写',
        required: false,
        default: false,
        example: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], EntityHighlightDto.prototype, "caseSensitive", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '是否包含别名匹配',
        required: false,
        default: true,
        example: true
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], EntityHighlightDto.prototype, "includeAliases", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '是否包含翻译匹配',
        required: false,
        default: true,
        example: true
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], EntityHighlightDto.prototype, "includeTranslations", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '最小匹配置信度',
        required: false,
        default: 0.8,
        minimum: 0,
        maximum: 1,
        example: 0.8
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(1),
    __metadata("design:type", Number)
], EntityHighlightDto.prototype, "minConfidence", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '最大匹配数量',
        required: false,
        minimum: 1,
        example: 100
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    __metadata("design:type", Number)
], EntityHighlightDto.prototype, "maxMatches", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '要匹配的实体类型',
        enum: entity_types_1.EntityType,
        isArray: true,
        required: false,
        example: [entity_types_1.EntityType.SATELLITE, entity_types_1.EntityType.ROCKET]
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsEnum)(entity_types_1.EntityType, { each: true }),
    __metadata("design:type", Array)
], EntityHighlightDto.prototype, "entityTypes", void 0);
exports.EntityHighlightDto = EntityHighlightDto;
class EntitySearchDto {
    constructor() {
        this.includeAliases = true;
        this.includeTranslations = true;
        this.limit = 50;
        this.offset = 0;
    }
}
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '搜索关键词',
        required: false,
        example: 'Falcon'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], EntitySearchDto.prototype, "keyword", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '实体类型',
        enum: entity_types_1.EntityType,
        required: false,
        example: entity_types_1.EntityType.ROCKET
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(entity_types_1.EntityType),
    __metadata("design:type", String)
], EntitySearchDto.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '是否包含别名搜索',
        required: false,
        default: true,
        example: true
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], EntitySearchDto.prototype, "includeAliases", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '是否包含翻译搜索',
        required: false,
        default: true,
        example: true
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], EntitySearchDto.prototype, "includeTranslations", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '返回数量限制',
        required: false,
        default: 50,
        minimum: 1,
        maximum: 1000,
        example: 50
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(1000),
    __metadata("design:type", Number)
], EntitySearchDto.prototype, "limit", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '偏移量',
        required: false,
        default: 0,
        minimum: 0,
        example: 0
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], EntitySearchDto.prototype, "offset", void 0);
exports.EntitySearchDto = EntitySearchDto;
class EntitySyncDto {
    constructor() {
        this.forceSync = false;
    }
}
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '要同步的实体类型',
        enum: entity_types_1.EntityType,
        isArray: true,
        required: false,
        example: [entity_types_1.EntityType.SATELLITE, entity_types_1.EntityType.ROCKET]
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsEnum)(entity_types_1.EntityType, { each: true }),
    __metadata("design:type", Array)
], EntitySyncDto.prototype, "entityTypes", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '是否强制重新同步',
        required: false,
        default: false,
        example: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], EntitySyncDto.prototype, "forceSync", void 0);
exports.EntitySyncDto = EntitySyncDto;
//# sourceMappingURL=entity.dto.js.map