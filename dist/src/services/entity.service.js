"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var EntityService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.EntityService = void 0;
const common_1 = require("@nestjs/common");
const entity_types_1 = require("../types/entity.types");
const elasticsearch_satellite_service_1 = require("../elasticsearch/services/elasticsearch.satellite.service");
const elasticsearch_launch_service_1 = require("../elasticsearch/services/elasticsearch.launch.service");
const elasticsearch_constellation_service_1 = require("../elasticsearch/services/elasticsearch.constellation.service");
const elasticsearch_rocket_service_1 = require("../elasticsearch/services/elasticsearch.rocket.service");
let EntityService = EntityService_1 = class EntityService {
    constructor(satelliteService, launchService, constellationService, rocketService) {
        this.satelliteService = satelliteService;
        this.launchService = launchService;
        this.constellationService = constellationService;
        this.rocketService = rocketService;
        this.logger = new common_1.Logger(EntityService_1.name);
        this.entityCache = new Map();
        this.lastSyncTime = new Map();
    }
    async getAllEntities() {
        const allEntities = [];
        for (const type of Object.values(entity_types_1.EntityType)) {
            const entities = await this.getEntitiesByType(type);
            allEntities.push(...entities.entities);
        }
        return {
            entities: allEntities,
            total: allEntities.length,
            lastUpdated: new Date()
        };
    }
    async getEntitiesByType(type) {
        try {
            if (this.entityCache.has(type)) {
                const cachedEntities = this.entityCache.get(type);
                const lastSync = this.lastSyncTime.get(type);
                if (lastSync && (Date.now() - lastSync.getTime()) < 3600000) {
                    return {
                        entities: cachedEntities,
                        total: cachedEntities.length,
                        type,
                        lastUpdated: lastSync
                    };
                }
            }
            const entities = await this.syncEntitiesFromSource(type);
            this.entityCache.set(type, entities);
            this.lastSyncTime.set(type, new Date());
            return {
                entities,
                total: entities.length,
                type,
                lastUpdated: new Date()
            };
        }
        catch (error) {
            this.logger.error(`获取实体失败，类型: ${type}, 错误: ${error.message}`, error.stack);
            throw error;
        }
    }
    async searchEntities(query) {
        const { keyword, type, includeAliases = true, includeTranslations = true, limit = 50, offset = 0 } = query;
        let entities = [];
        if (type) {
            const typeEntities = await this.getEntitiesByType(type);
            entities = typeEntities.entities;
        }
        else {
            const allEntities = await this.getAllEntities();
            entities = allEntities.entities;
        }
        if (keyword) {
            const lowerKeyword = keyword.toLowerCase();
            entities = entities.filter(entity => {
                var _a, _b;
                if (entity.name.toLowerCase().includes(lowerKeyword)) {
                    return true;
                }
                if (includeAliases && entity.aliases) {
                    if (entity.aliases.some(alias => alias.toLowerCase().includes(lowerKeyword))) {
                        return true;
                    }
                }
                if (includeTranslations && entity.translations) {
                    if (((_a = entity.translations.en) === null || _a === void 0 ? void 0 : _a.toLowerCase().includes(lowerKeyword)) ||
                        ((_b = entity.translations.cn) === null || _b === void 0 ? void 0 : _b.toLowerCase().includes(lowerKeyword))) {
                        return true;
                    }
                }
                return false;
            });
        }
        const paginatedEntities = entities.slice(offset, offset + limit);
        return {
            entities: paginatedEntities,
            total: entities.length,
            type,
            lastUpdated: new Date()
        };
    }
    async getSyncStatus() {
        const statuses = [];
        for (const type of Object.values(entity_types_1.EntityType)) {
            const lastSync = this.lastSyncTime.get(type);
            const entities = this.entityCache.get(type) || [];
            statuses.push({
                type,
                lastSyncTime: lastSync || new Date(0),
                totalEntities: entities.length,
                status: lastSync ? 'success' : 'failed'
            });
        }
        return statuses;
    }
    async forceSyncEntities(types) {
        const typesToSync = types || Object.values(entity_types_1.EntityType);
        const results = [];
        for (const type of typesToSync) {
            try {
                this.logger.log(`开始同步实体类型: ${type}`);
                const entities = await this.syncEntitiesFromSource(type);
                this.entityCache.set(type, entities);
                this.lastSyncTime.set(type, new Date());
                results.push({
                    type,
                    lastSyncTime: new Date(),
                    totalEntities: entities.length,
                    status: 'success'
                });
                this.logger.log(`实体同步完成: ${type}, 数量: ${entities.length}`);
            }
            catch (error) {
                this.logger.error(`实体同步失败: ${type}, 错误: ${error.message}`, error.stack);
                results.push({
                    type,
                    lastSyncTime: new Date(),
                    totalEntities: 0,
                    status: 'failed',
                    error: error.message
                });
            }
        }
        return results;
    }
    async syncEntitiesFromSource(type) {
        switch (type) {
            case entity_types_1.EntityType.SATELLITE:
                return this.syncSatelliteEntities();
            case entity_types_1.EntityType.ROCKET:
                return this.syncRocketEntities();
            case entity_types_1.EntityType.CONSTELLATION:
                return this.syncConstellationEntities();
            case entity_types_1.EntityType.LAUNCH_SITE:
                return this.syncLaunchSiteEntities();
            case entity_types_1.EntityType.COMPANY:
                return this.syncCompanyEntities();
            default:
                this.logger.warn(`不支持的实体类型: ${type}`);
                return [];
        }
    }
    async syncSatelliteEntities() {
        try {
            const satelliteNames = await this.satelliteService.getSatelliteNames();
            return satelliteNames.map((name, index) => ({
                id: `satellite_${index}`,
                name,
                type: entity_types_1.EntityType.SATELLITE,
                createdAt: new Date(),
                updatedAt: new Date()
            }));
        }
        catch (error) {
            this.logger.error(`同步卫星实体失败: ${error.message}`, error.stack);
            return [];
        }
    }
    async syncRocketEntities() {
        try {
            const rocketNames = await this.launchService.getRocketNames();
            return rocketNames.map((name, index) => ({
                id: `rocket_${index}`,
                name,
                type: entity_types_1.EntityType.ROCKET,
                createdAt: new Date(),
                updatedAt: new Date()
            }));
        }
        catch (error) {
            this.logger.error(`同步火箭实体失败: ${error.message}`, error.stack);
            return [];
        }
    }
    async syncConstellationEntities() {
        try {
            const constellationNames = await this.constellationService.getConstellationNames();
            return constellationNames.constellationNames.map((name, index) => ({
                id: `constellation_${index}`,
                name,
                type: entity_types_1.EntityType.CONSTELLATION,
                createdAt: new Date(),
                updatedAt: new Date()
            }));
        }
        catch (error) {
            this.logger.error(`同步星座实体失败: ${error.message}`, error.stack);
            return [];
        }
    }
    async syncLaunchSiteEntities() {
        try {
            const siteNames = await this.launchService.getSiteNames();
            return siteNames.map((name, index) => ({
                id: `launch_site_${index}`,
                name,
                type: entity_types_1.EntityType.LAUNCH_SITE,
                createdAt: new Date(),
                updatedAt: new Date()
            }));
        }
        catch (error) {
            this.logger.error(`同步发射场实体失败: ${error.message}`, error.stack);
            return [];
        }
    }
    async syncCompanyEntities() {
        try {
            const organizations = await this.constellationService.getConstellationOrganizations();
            return organizations.organizations.map((name, index) => ({
                id: `company_${index}`,
                name,
                type: entity_types_1.EntityType.COMPANY,
                createdAt: new Date(),
                updatedAt: new Date()
            }));
        }
        catch (error) {
            this.logger.error(`同步公司实体失败: ${error.message}`, error.stack);
            return [];
        }
    }
    clearCache() {
        this.entityCache.clear();
        this.lastSyncTime.clear();
        this.logger.log('实体缓存已清除');
    }
};
EntityService = EntityService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [elasticsearch_satellite_service_1.ElasticsearchSatelliteService,
        elasticsearch_launch_service_1.ElasticsearchLaunchService,
        elasticsearch_constellation_service_1.ElasticsearchConstellationService,
        elasticsearch_rocket_service_1.ElasticsearchRocketService])
], EntityService);
exports.EntityService = EntityService;
//# sourceMappingURL=entity.service.js.map