import { EntityType, EntityCollectionResponse, EntitySearchQuery, EntitySyncStatus } from '../types/entity.types';
import { ElasticsearchSatelliteService } from '../elasticsearch/services/elasticsearch.satellite.service';
import { ElasticsearchLaunchService } from '../elasticsearch/services/elasticsearch.launch.service';
import { ElasticsearchConstellationService } from '../elasticsearch/services/elasticsearch.constellation.service';
import { ElasticsearchRocketService } from '../elasticsearch/services/elasticsearch.rocket.service';
export declare class EntityService {
    private readonly satelliteService;
    private readonly launchService;
    private readonly constellationService;
    private readonly rocketService;
    private readonly logger;
    private entityCache;
    private lastSyncTime;
    constructor(satelliteService: ElasticsearchSatelliteService, launchService: ElasticsearchLaunchService, constellationService: ElasticsearchConstellationService, rocketService: ElasticsearchRocketService);
    getAllEntities(): Promise<EntityCollectionResponse>;
    getEntitiesByType(type: EntityType): Promise<EntityCollectionResponse>;
    searchEntities(query: EntitySearchQuery): Promise<EntityCollectionResponse>;
    getSyncStatus(): Promise<EntitySyncStatus[]>;
    forceSyncEntities(types?: EntityType[]): Promise<EntitySyncStatus[]>;
    private syncEntitiesFromSource;
    private syncSatelliteEntities;
    private syncRocketEntities;
    private syncConstellationEntities;
    private syncLaunchSiteEntities;
    private syncCompanyEntities;
    clearCache(): void;
}
