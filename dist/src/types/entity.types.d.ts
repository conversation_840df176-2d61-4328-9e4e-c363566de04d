export declare enum EntityType {
    SATELLITE = "satellite",
    ROCKET = "rocket",
    CONSTELLATION = "constellation",
    COMPANY = "company",
    LAUNCH_SITE = "launch_site",
    MISSION = "mission",
    ORGANIZATION = "organization"
}
export interface Entity {
    id: string;
    name: string;
    type: EntityType;
    aliases?: string[];
    translations?: {
        en?: string;
        cn?: string;
    };
    metadata?: {
        [key: string]: any;
    };
    createdAt?: Date;
    updatedAt?: Date;
}
export interface EntityMatch {
    entity: Entity;
    matchedText: string;
    startIndex: number;
    endIndex: number;
    confidence: number;
    isExactMatch: boolean;
}
export interface EntityHighlightConfig {
    wholeWordOnly: boolean;
    caseSensitive: boolean;
    includeAliases: boolean;
    includeTranslations: boolean;
    minConfidence: number;
    maxMatches?: number;
}
export interface HighlightedText {
    originalText: string;
    highlightedText: string;
    entities: EntityMatch[];
    metadata: {
        totalMatches: number;
        processingTime: number;
        config: EntityHighlightConfig;
    };
}
export interface EntityCollectionResponse {
    entities: Entity[];
    total: number;
    type?: EntityType;
    lastUpdated: Date;
}
export interface EntitySearchQuery {
    keyword?: string;
    type?: EntityType;
    includeAliases?: boolean;
    includeTranslations?: boolean;
    limit?: number;
    offset?: number;
}
export interface EntitySyncStatus {
    type: EntityType;
    lastSyncTime: Date;
    totalEntities: number;
    status: 'success' | 'failed' | 'in_progress';
    error?: string;
}
export interface EntityHighlightRequest {
    text: string;
    language?: 'en' | 'cn' | 'auto';
    config?: Partial<EntityHighlightConfig>;
    entityTypes?: EntityType[];
}
export interface EntityHighlightResponse {
    success: boolean;
    data?: HighlightedText;
    error?: string;
    timestamp: string;
}
